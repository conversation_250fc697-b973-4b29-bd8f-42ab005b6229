import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '@rpa-project/shared';
import { Permission, hasPermission, AuthContext, UserRole } from '../types/permissions';
import { jwtService } from '../services/auth/jwtService';
import { authService } from '../services/auth/authService';
import { securityLogService } from '../services/auth/securityLogService';
import { apiKeyConfig } from '../config/security';

// Extend Express Request to include auth context
declare global {
  namespace Express {
    interface Request {
      auth?: AuthContext;
    }
  }
}

/**
 * Authentication middleware - verifies JWT token or API key
 */
export function authenticate(req: Request, res: Response, next: NextFunction): any {
  const authHeader = req.headers.authorization;
  const apiKeyHeader = req.headers[apiKeyConfig.headerName.toLowerCase()] as string;

  // Try JWT authentication first
  if (authHeader) {
    authenticateJWT(req, res, next);
    return;
  }

  // Try API key authentication
  if (apiKeyHeader) {
    authenticateApiKey(req, res, next);
    return;
  }

  // No authentication provided
  securityLogService.log({
    type: 'unauthorized_access',
    ip: getClientIP(req),
    userAgent: req.headers['user-agent'],
    endpoint: req.path,
    method: req.method,
    statusCode: 401,
    message: 'No authentication provided',
  });

  const response: ApiResponse = {
    success: false,
    error: 'Authentication required',
  };

  res.status(401).json(response);
}

/**
 * JWT authentication
 */
async function authenticateJWT(req: Request, res: Response, next: NextFunction): Promise<void> {
  try {
    const token = jwtService.extractTokenFromHeader(req.headers.authorization);
    if (!token) {
      throw new Error('Invalid authorization header format');
    }

    console.log('🔐 Verifying JWT token:', token.substring(0, 20) + '...');
    const payload = jwtService.verifyToken(token);
    console.log('🔐 JWT payload:', payload);
    
    if (payload.type === 'user') {
      const user = await authService.getUserById(payload.userId);
      if (!user || !user.isActive) {
        throw new Error('User not found or inactive');
      }

      req.auth = {
        user,
        role: user.role,
        type: 'user',
      };
    } else if (payload.type === 'api') {
      // For API key tokens, we need to verify the API key is still valid
      req.auth = {
        role: payload.role,
        type: 'api',
      };
    }

    next();
  } catch (error) {
    await securityLogService.log({
      type: 'unauthorized_access',
      ip: getClientIP(req),
      userAgent: req.headers['user-agent'],
      endpoint: req.path,
      method: req.method,
      statusCode: 401,
      message: `JWT authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    });

    const response: ApiResponse = {
      success: false,
      error: 'Invalid or expired token',
    };

    res.status(401).json(response);
  }
}

/**
 * API key authentication
 */
async function authenticateApiKey(req: Request, res: Response, next: NextFunction): Promise<void> {
  try {
    const apiKeyValue = req.headers[apiKeyConfig.headerName.toLowerCase()] as string;
    if (!apiKeyValue) {
      throw new Error('API key not provided');
    }

    const apiKey = await authService.verifyApiKey(apiKeyValue);
    if (!apiKey) {
      throw new Error('Invalid or expired API key');
    }

    req.auth = {
      apiKey,
      role: apiKey.role,
      type: 'api',
    };

    // Log API key usage
    await securityLogService.log({
      type: 'api_key_used',
      apiKeyId: apiKey.id,
      ip: getClientIP(req),
      userAgent: req.headers['user-agent'],
      endpoint: req.path,
      method: req.method,
      statusCode: 200,
      message: `API key ${apiKey.name} used successfully`,
    });

    next();
  } catch (error) {
    await securityLogService.log({
      type: 'unauthorized_access',
      ip: getClientIP(req),
      userAgent: req.headers['user-agent'],
      endpoint: req.path,
      method: req.method,
      statusCode: 401,
      message: `API key authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    });

    const response: ApiResponse = {
      success: false,
      error: 'Invalid or expired API key',
    };

    res.status(401).json(response);
  }
}

/**
 * Authorization middleware - checks if user has required permission
 */
export function authorize(permission: Permission) {
  return (req: Request, res: Response, next: NextFunction): any => {
    if (!req.auth) {
      const response: ApiResponse = {
        success: false,
        error: 'Authentication required',
      };
      return res.status(401).json(response);
    }

    if (!hasPermission(req.auth.role, permission)) {
      securityLogService.log({
        type: 'unauthorized_access',
        userId: req.auth.user?.id,
        apiKeyId: req.auth.apiKey?.id,
        ip: getClientIP(req),
        userAgent: req.headers['user-agent'],
        endpoint: req.path,
        method: req.method,
        statusCode: 403,
        message: `Access denied: insufficient permissions for ${permission.resource}:${permission.action}`,
      });

      const response: ApiResponse = {
        success: false,
        error: 'Insufficient permissions',
      };
      return res.status(403).json(response);
    }

    next();
  };
}

/**
 * Role-based authorization middleware
 */
export function requireRole(roles: UserRole | UserRole[]) {
  const allowedRoles = Array.isArray(roles) ? roles : [roles];

  return (req: Request, res: Response, next: NextFunction): any => {
    if (!req.auth) {
      const response: ApiResponse = {
        success: false,
        error: 'Authentication required',
      };
      return res.status(401).json(response);
    }

    if (!allowedRoles.includes(req.auth.role)) {
      securityLogService.log({
        type: 'unauthorized_access',
        userId: req.auth.user?.id,
        apiKeyId: req.auth.apiKey?.id,
        ip: getClientIP(req),
        userAgent: req.headers['user-agent'],
        endpoint: req.path,
        method: req.method,
        statusCode: 403,
        message: `Access denied: role ${req.auth.role} not in allowed roles [${allowedRoles.join(', ')}]`,
      });

      const response: ApiResponse = {
        success: false,
        error: 'Insufficient role permissions',
      };
      return res.status(403).json(response);
    }

    next();
  };
}

/**
 * Optional authentication middleware - doesn't fail if no auth provided
 */
export function optionalAuth(req: Request, res: Response, next: NextFunction): any {
  const authHeader = req.headers.authorization;
  const apiKeyHeader = req.headers[apiKeyConfig.headerName.toLowerCase()] as string;

  if (authHeader || apiKeyHeader) {
    authenticate(req, res, next);
  } else {
    next();
  }
}

/**
 * Admin only middleware
 */
export const requireAdmin = requireRole('admin');

/**
 * Operator or admin middleware
 */
export const requireOperator = requireRole(['admin', 'operator']);

/**
 * Any authenticated user middleware
 */
export const requireAuth = authenticate;

/**
 * Get client IP address from request
 */
function getClientIP(req: Request): string {
  return (
    (req.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
    (req.headers['x-real-ip'] as string) ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    '127.0.0.1'
  );
}

/**
 * Middleware to log all requests for security monitoring
 */
export function securityLogger(req: Request, res: Response, next: NextFunction): void {
  const startTime = Date.now();

  // Override res.json to capture status code
  const originalJson = res.json;
  res.json = function(body: any) {
    const duration = Date.now() - startTime;
    
    // Log request for security monitoring
    securityLogService.log({
      type: res.statusCode >= 400 ? 'unauthorized_access' : 'api_key_used',
      userId: req.auth?.user?.id,
      apiKeyId: req.auth?.apiKey?.id,
      ip: getClientIP(req),
      userAgent: req.headers['user-agent'],
      endpoint: req.path,
      method: req.method,
      statusCode: res.statusCode,
      message: `${req.method} ${req.path} - ${res.statusCode} (${duration}ms)`,
      metadata: {
        duration,
        userAgent: req.headers['user-agent'],
        contentLength: JSON.stringify(body).length,
      },
    });

    return originalJson.call(this, body);
  };

  next();
}

/**
 * Middleware to check if user account is active
 */
export function requireActiveUser(req: Request, res: Response, next: NextFunction): any {
  if (!req.auth?.user) {
    const response: ApiResponse = {
      success: false,
      error: 'User authentication required',
    };
    return res.status(401).json(response);
  }

  if (!req.auth.user.isActive) {
    securityLogService.log({
      type: 'unauthorized_access',
      userId: req.auth.user.id,
      ip: getClientIP(req),
      userAgent: req.headers['user-agent'],
      endpoint: req.path,
      method: req.method,
      statusCode: 403,
      message: `Access denied: user account ${req.auth.user.username} is inactive`,
    });

    const response: ApiResponse = {
      success: false,
      error: 'User account is inactive',
    };
    return res.status(403).json(response);
  }

  next();
}
