import { RpaStepBase } from './base';

// AI processing steps
export interface ExtractPdfValuesStep extends RpaStepBase {
  type: 'extractPdfValues';
  base64Input: string; // Base64 PDF data or variable reference
  prompt: string; // User prompt for LLM processing to extract specific values
  variableName?: string; // Variable name to store the extracted JSON result (optional, defaults to 'extractedData')
}

// Script processing step
export interface ScriptStep extends RpaStepBase {
  type: 'script';
  code: string; // JavaScript code to execute
  variableName?: string; // Variable name to store the script result (optional, defaults to 'scriptResult')
  timeout?: number; // Timeout in milliseconds (default: 60000)
}
