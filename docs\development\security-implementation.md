# Säkerhetsimplementation för RPA-applikationen

## Översikt

RPA-applikationen har implementerat en omfattande säkerhetslösning som skyddar alla interna API:er med autentisering, auktorisering och säkerhetsövervakning.

## Säkerhetsarkitektur

### 1. Autentisering (Authentication)

#### JWT-baserad autentisering
- **Access tokens**: Kortlivade tokens (24h) för API-åtkomst
- **Refresh tokens**: Långlivade tokens (7d) för förnyelse
- **Säker token-hantering**: Automatisk cleanup av utgångna tokens

#### API-nyckel autentisering
- **Maskin-till-maskin**: För automatiserade system
- **Rollbaserade nycklar**: Olika behörighetsnivåer
- **Säker lagring**: Hashade nycklar med bcrypt

### 2. Auktorisering (Authorization)

#### Rollbaserad åtkomstkontroll (RBAC)
```typescript
// Roller och behörigheter
admin     - Full åtkomst till allt
operator  - Kan köra flöden och hantera data
viewer    - Endast läsåtkomst
api       - Begränsad åtkomst för API-integration
```

#### Granulära behörigheter
- **Resursbaserade**: flows, executions, schedules, customers, etc.
- **Åtgärdsbaserade**: read, create, update, delete, execute
- **Automatisk validering**: Middleware kontrollerar behörigheter

### 3. Rate Limiting

#### Flera nivåer av begränsningar
```typescript
// Global rate limiting
1000 requests per 15 minuter (alla användare)

// Autentisering rate limiting  
10 login-försök per 15 minuter

// API rate limiting
100 requests per minut (API-nycklar)
```

#### Intelligent begränsning
- **Per användare/IP**: Individuella begränsningar
- **Progressiv lockout**: Ökande väntetider
- **Whitelist-stöd**: Undantag för betrodda IP:er

### 4. Säkerhetsvalidering

#### Input sanitization
- **XSS-skydd**: Rengöring av användarinput
- **Injection-skydd**: SQL och kod-injection prevention
- **Storleksbegränsningar**: Förhindrar DoS-attacker

#### CSRF-skydd
- **Token-validering**: Unika tokens per session
- **Origin-validering**: Kontroll av request-ursprung
- **Automatisk generering**: Säkra CSRF-tokens

#### Suspicious activity detection
- **Pattern matching**: Identifierar kända attack-verktyg
- **Anomali-detektion**: Ovanliga request-mönster
- **Automatisk blockering**: Stoppar misstänkta requests

### 5. Säkerhetsövervakning

#### Real-time monitoring
- **Säkerhetsloggar**: Alla säkerhetshändelser loggas
- **Automatisk analys**: Kontinuerlig övervakning
- **Alert-system**: Varningar för säkerhetshot

#### Säkerhetsmetriker
- **Misslyckade inloggningar**: Spårning av brute force
- **Misstänkt aktivitet**: Identifiering av attacker
- **Rate limit-överträdelser**: DoS-försök
- **Felfrekvens**: Systemhälsa

## API-säkerhet

### Skyddade endpoints

Alla API endpoints kräver nu autentisering:

```typescript
// Exempel på skyddad endpoint
router.get('/api/flows', 
  authenticate,                    // Kräver giltig token/API-nyckel
  authorize(PERMISSIONS.FLOWS_READ), // Kräver läsbehörighet
  asyncHandler(async (req, res) => {
    // Endpoint-logik
  })
);
```

### Säkerhetsheaders

Automatiska säkerhetsheaders på alla responses:
```http
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Referrer-Policy: strict-origin-when-cross-origin
Strict-Transport-Security: max-age=31536000
```

### CORS-konfiguration

Strikt CORS-policy med whitelist:
```typescript
// Tillåtna origins
allowedOrigins: [
  'http://localhost:3000',
  'http://localhost:3001',
  'https://your-domain.com'
]
```

## Konfiguration

### Miljövariabler

```bash
# JWT-konfiguration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
AUTH_RATE_LIMIT_MAX_REQUESTS=10

# Säkerhetsinställningar
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=30
SESSION_TIMEOUT=1440

# CORS och origins
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
IP_WHITELIST=127.0.0.1,::1

# Default admin-användare
DEFAULT_ADMIN_PASSWORD=admin123!
```

### Säkerhetskonfiguration

```typescript
// Lösenordspolicy
minLength: 8
requireUppercase: true
requireLowercase: true  
requireNumbers: true
requireSpecialChars: true
preventCommonPasswords: true
```

## Användning

### Autentisering

#### Användarinloggning
```bash
POST /api/auth/login
{
  "username": "admin",
  "password": "your-password"
}

Response:
{
  "success": true,
  "data": {
    "user": { ... },
    "token": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "abc123...",
    "expiresAt": "2024-01-01T12:00:00Z"
  }
}
```

#### API-nyckel användning
```bash
GET /api/flows
Headers:
  X-API-Key: rpa_api_abc123...
```

#### Token förnyelse
```bash
POST /api/auth/refresh
{
  "refreshToken": "abc123..."
}
```

### Användarhantering

#### Skapa användare (admin)
```bash
POST /api/auth/users
{
  "username": "operator1",
  "email": "<EMAIL>", 
  "password": "SecurePass123!",
  "role": "operator"
}
```

#### Skapa API-nyckel (admin)
```bash
POST /api/auth/api-keys
{
  "name": "Integration API Key",
  "description": "För automatiserad integration",
  "role": "api",
  "expiresAt": "2025-01-01T00:00:00Z"
}
```

### Säkerhetsövervakning

#### Säkerhetsdashboard
```bash
GET /api/auth/security/dashboard
```

#### Säkerhetsvarningar
```bash
GET /api/auth/security/alerts
GET /api/auth/security/alerts?type=high
```

#### Säkerhetshälsa
```bash
GET /api/auth/security/health
```

## Säkerhetsloggar

### Loggtyper

- `login_success` - Lyckad inloggning
- `login_failed` - Misslyckad inloggning  
- `logout` - Utloggning
- `token_refresh` - Token förnyelse
- `api_key_used` - API-nyckel användning
- `unauthorized_access` - Obehörig åtkomst
- `rate_limit_exceeded` - Rate limit överskriden
- `suspicious_activity` - Misstänkt aktivitet
- `password_changed` - Lösenord ändrat
- `user_created` - Användare skapad
- `api_key_created` - API-nyckel skapad

### Loggformat

```json
{
  "id": "log_123",
  "type": "login_failed",
  "userId": "user_456",
  "ip": "*************",
  "userAgent": "Mozilla/5.0...",
  "endpoint": "/api/auth/login",
  "method": "POST",
  "statusCode": 401,
  "message": "Failed login attempt for: admin",
  "metadata": {
    "attempt": 3,
    "lockoutTime": "2024-01-01T12:30:00Z"
  },
  "createdAt": "2024-01-01T12:00:00Z"
}
```

## Säkerhetsvarningar

### Automatiska varningar

Systemet genererar automatiskt varningar för:

- **Högt antal misslyckade inloggningar** (>20/timme)
- **Misstänkt aktivitet** (>5/timme)
- **Rate limit-överträdelser** (>50/timme)
- **Hög felfrekvens** (>10%)
- **Obehörig åtkomst** (>10/timme)

### Varningsnivåer

- **HIGH**: Kritiska säkerhetshot som kräver omedelbar åtgärd
- **MEDIUM**: Potentiella problem som bör undersökas
- **LOW**: Informativa varningar för övervakning

## Bästa praxis

### För utvecklare

1. **Använd alltid autentisering**: Alla nya endpoints ska skyddas
2. **Implementera behörigheter**: Använd granulära permissions
3. **Validera input**: Sanitisera all användarinput
4. **Logga säkerhetshändelser**: Spåra alla säkerhetsrelaterade åtgärder
5. **Testa säkerhet**: Inkludera säkerhetstester i CI/CD

### För administratörer

1. **Övervaka säkerhetsloggar**: Granska regelbundet
2. **Hantera användare**: Ta bort inaktiva konton
3. **Rotera API-nycklar**: Byt ut nycklar regelbundet
4. **Uppdatera lösenord**: Använd starka lösenord
5. **Konfigurera whitelist**: Begränsa åtkomst till kända IP:er

### För operatörer

1. **Använd starka lösenord**: Följ lösenordspolicyn
2. **Logga ut säkert**: Använd logout-funktionen
3. **Rapportera misstänkt aktivitet**: Kontakta admin vid problem
4. **Håll credentials säkra**: Dela aldrig inloggningsuppgifter

## Felsökning

### Vanliga problem

#### "Authentication required"
- Kontrollera att Authorization header är korrekt
- Verifiera att token inte har gått ut
- Kontrollera API-nyckel format

#### "Insufficient permissions"
- Kontrollera användarens roll
- Verifiera endpoint-behörigheter
- Kontakta admin för rolluppdatering

#### "Rate limit exceeded"
- Vänta tills rate limit-fönstret återställs
- Kontrollera om IP är blockerad
- Kontakta admin för whitelist

#### "CSRF token validation failed"
- Kontrollera att CSRF-token skickas
- Verifiera session-giltighet
- Uppdatera CSRF-token

### Säkerhetsincidenter

Vid säkerhetsincidenter:

1. **Identifiera hotet**: Analysera säkerhetsloggar
2. **Isolera problemet**: Blockera misstänkta IP:er
3. **Dokumentera incidenten**: Spara all relevant information
4. **Åtgärda sårbarheter**: Implementera förbättringar
5. **Övervaka efteråt**: Öka övervakning temporärt

## Framtida förbättringar

### Planerade funktioner

- **Multi-factor authentication (MFA)**: 2FA-stöd
- **Single Sign-On (SSO)**: Integration med företags-SSO
- **Geo-blocking**: IP-baserad geografisk begränsning
- **Advanced threat detection**: ML-baserad anomali-detektion
- **Audit trails**: Detaljerad spårning av alla åtgärder
- **Compliance reporting**: Automatiska säkerhetsrapporter
