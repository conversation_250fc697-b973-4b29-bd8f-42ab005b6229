import bcrypt from 'bcrypt';
import crypto from 'crypto';
import { generateId } from '@rpa-project/shared';
import {
  User,
  ApiKey,
  UserRole
} from '../../types/permissions';

// Define request/response types locally
export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  role: UserRole;
}

export interface UpdateUserRequest {
  username?: string;
  email?: string;
  role?: UserRole;
  isActive?: boolean;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: Omit<User, 'password'>;
  token: string;
  refreshToken: string;
  expiresAt: Date;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface CreateApiKeyRequest {
  name: string;
  description?: string;
  role: UserRole;
  expiresAt?: Date;
}

export interface UpdateApiKeyRequest {
  name?: string;
  description?: string;
  role?: UserRole;
  isActive?: boolean;
  expiresAt?: Date;
}

export interface ApiKeyResponse {
  apiKey: Omit<ApiKey, 'keyHash'>;
  key: string; // Only returned on creation
}
import { jwtService, TokenPair } from './jwtService';
import { securityConfig, validatePassword, isCommonWeakPassword, apiKeyConfig } from '../../config/security';
import { securityLogService } from './securityLogService';

/**
 * Authentication Service
 */
export class AuthService {
  private users = new Map<string, User>();
  private apiKeys = new Map<string, ApiKey>();
  private loginAttempts = new Map<string, { count: number; lastAttempt: Date; lockedUntil?: Date }>();

  constructor() {
    // Create default admin user if none exists
    this.initializeDefaultUser();
  }

  /**
   * Initialize default admin user
   */
  private async initializeDefaultUser(): Promise<void> {
    if (this.users.size === 0) {
      const defaultPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'admin123!';
      
      const adminUser: User = {
        id: generateId(),
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const hashedPassword = await bcrypt.hash(defaultPassword, securityConfig.security.bcryptRounds);
      this.users.set(adminUser.id, { ...adminUser, password: hashedPassword } as any);

      console.log('🔐 Default admin user created:');
      console.log(`   Username: ${adminUser.username}`);
      console.log(`   Password: ${defaultPassword}`);
      console.log('   ⚠️  Please change the default password immediately!');
    }
  }

  /**
   * Register new user
   */
  async registerUser(request: CreateUserRequest): Promise<User> {
    // Validate password
    const passwordErrors = validatePassword(request.password, {
      username: request.username,
      email: request.email,
    });

    if (passwordErrors.length > 0) {
      throw new Error(`Password validation failed: ${passwordErrors.join(', ')}`);
    }

    if (isCommonWeakPassword(request.password)) {
      throw new Error('Password is too common and easily guessable');
    }

    // Check if username or email already exists
    const existingUser = Array.from(this.users.values()).find(
      user => user.username === request.username || user.email === request.email
    );

    if (existingUser) {
      throw new Error('Username or email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(request.password, securityConfig.security.bcryptRounds);

    // Create user
    const user: User = {
      id: generateId(),
      username: request.username,
      email: request.email,
      role: request.role,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.users.set(user.id, { ...user, password: hashedPassword } as any);

    await securityLogService.log({
      type: 'user_created',
      userId: user.id,
      ip: '127.0.0.1', // This should be passed from the request
      endpoint: '/api/auth/register',
      method: 'POST',
      statusCode: 201,
      message: `User ${user.username} created with role ${user.role}`,
    });

    return user;
  }

  /**
   * Authenticate user login
   */
  async login(request: LoginRequest, ip: string): Promise<LoginResponse> {
    const identifier = request.username.toLowerCase();

    // Check for account lockout
    const attempts = this.loginAttempts.get(identifier);
    if (attempts?.lockedUntil && attempts.lockedUntil > new Date()) {
      await securityLogService.log({
        type: 'login_failed',
        ip,
        endpoint: '/api/auth/login',
        method: 'POST',
        statusCode: 423,
        message: `Login attempt on locked account: ${request.username}`,
      });
      throw new Error('Account is temporarily locked due to too many failed login attempts');
    }

    // Find user
    const user = Array.from(this.users.values()).find(
      u => u.username.toLowerCase() === identifier || u.email.toLowerCase() === identifier
    );

    if (!user || !user.isActive) {
      await this.recordFailedLogin(identifier, ip);
      throw new Error('Invalid username or password');
    }

    // Verify password
    const userWithPassword = this.users.get(user.id) as any;
    const isValidPassword = await bcrypt.compare(request.password, userWithPassword.password);

    if (!isValidPassword) {
      await this.recordFailedLogin(identifier, ip);
      throw new Error('Invalid username or password');
    }

    // Reset login attempts on successful login
    this.loginAttempts.delete(identifier);

    // Update last login
    user.lastLoginAt = new Date();
    user.updatedAt = new Date();
    this.users.set(user.id, { ...userWithPassword, ...user });

    // Generate tokens
    const tokenPair = jwtService.generateTokenPair(user);

    await securityLogService.log({
      type: 'login_success',
      userId: user.id,
      ip,
      endpoint: '/api/auth/login',
      method: 'POST',
      statusCode: 200,
      message: `User ${user.username} logged in successfully`,
    });

    return {
      user,
      token: tokenPair.accessToken,
      refreshToken: tokenPair.refreshToken,
      expiresAt: tokenPair.expiresAt,
    };
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string, ip: string): Promise<TokenPair | null> {
    const tokenPair = await jwtService.refreshAccessToken(refreshToken, (id) => this.getUserById(id));

    if (tokenPair) {
      await securityLogService.log({
        type: 'token_refresh',
        ip,
        endpoint: '/api/auth/refresh',
        method: 'POST',
        statusCode: 200,
        message: 'Access token refreshed successfully',
      });
    }

    return tokenPair;
  }

  /**
   * Logout user
   */
  async logout(refreshToken: string, userId: string, ip: string): Promise<void> {
    jwtService.revokeRefreshToken(refreshToken);

    await securityLogService.log({
      type: 'logout',
      userId,
      ip,
      endpoint: '/api/auth/logout',
      method: 'POST',
      statusCode: 200,
      message: 'User logged out successfully',
    });
  }

  /**
   * Change user password
   */
  async changePassword(userId: string, request: ChangePasswordRequest, ip: string): Promise<void> {
    const userWithPassword = this.users.get(userId) as any;
    if (!userWithPassword) {
      throw new Error('User not found');
    }

    // Verify current password
    const isValidCurrentPassword = await bcrypt.compare(request.currentPassword, userWithPassword.password);
    if (!isValidCurrentPassword) {
      throw new Error('Current password is incorrect');
    }

    // Validate new password
    const passwordErrors = validatePassword(request.newPassword, {
      username: userWithPassword.username,
      email: userWithPassword.email,
    });

    if (passwordErrors.length > 0) {
      throw new Error(`Password validation failed: ${passwordErrors.join(', ')}`);
    }

    if (isCommonWeakPassword(request.newPassword)) {
      throw new Error('Password is too common and easily guessable');
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(request.newPassword, securityConfig.security.bcryptRounds);

    // Update password
    userWithPassword.password = hashedPassword;
    userWithPassword.updatedAt = new Date();
    this.users.set(userId, userWithPassword);

    // Revoke all existing tokens
    jwtService.revokeAllUserTokens(userId);

    await securityLogService.log({
      type: 'password_changed',
      userId,
      ip,
      endpoint: '/api/auth/change-password',
      method: 'POST',
      statusCode: 200,
      message: 'Password changed successfully',
    });
  }

  /**
   * Create API key
   */
  async createApiKey(request: CreateApiKeyRequest): Promise<ApiKeyResponse> {
    // Generate API key
    const keyPrefix = apiKeyConfig.prefixes.api;
    const keyBody = jwtService.generateSecureRandomString(apiKeyConfig.keyLength - keyPrefix.length);
    const apiKeyValue = keyPrefix + keyBody;

    // Hash the key for storage
    const keyHash = jwtService.hashApiKey(apiKeyValue);

    // Create API key record
    const apiKey: ApiKey = {
      id: generateId(),
      name: request.name,
      description: request.description,
      keyHash,
      role: request.role,
      isActive: true,
      expiresAt: request.expiresAt,
      createdAt: new Date(),
      updatedAt: new Date(),
      usageCount: 0,
    };

    this.apiKeys.set(apiKey.id, apiKey);

    await securityLogService.log({
      type: 'api_key_created',
      ip: '127.0.0.1', // This should be passed from the request
      endpoint: '/api/auth/api-keys',
      method: 'POST',
      statusCode: 201,
      message: `API key ${apiKey.name} created with role ${apiKey.role}`,
      metadata: { apiKeyId: apiKey.id },
    });

    return {
      apiKey: { ...apiKey, keyHash: undefined } as any,
      key: apiKeyValue,
    };
  }

  /**
   * Verify API key
   */
  async verifyApiKey(apiKeyValue: string): Promise<ApiKey | null> {
    const keyHash = jwtService.hashApiKey(apiKeyValue);

    for (const apiKey of this.apiKeys.values()) {
      if (apiKey.isActive && jwtService.verifyApiKeyHash(apiKeyValue, apiKey.keyHash)) {
        // Check expiration
        if (apiKey.expiresAt && apiKey.expiresAt < new Date()) {
          return null;
        }

        // Update usage statistics
        apiKey.usageCount++;
        apiKey.lastUsedAt = new Date();
        this.apiKeys.set(apiKey.id, apiKey);

        return apiKey;
      }
    }

    return null;
  }

  /**
   * Get user by ID
   */
  async getUserById(id: string): Promise<User | null> {
    const userWithPassword = this.users.get(id);
    if (!userWithPassword) {
      return null;
    }

    // Remove password from returned user
    const { password, ...user } = userWithPassword as any;
    return user;
  }

  /**
   * Get all users
   */
  async getAllUsers(): Promise<User[]> {
    return Array.from(this.users.values()).map(userWithPassword => {
      const { password, ...user } = userWithPassword as any;
      return user;
    });
  }

  /**
   * Update user
   */
  async updateUser(id: string, request: UpdateUserRequest): Promise<User> {
    const userWithPassword = this.users.get(id) as any;
    if (!userWithPassword) {
      throw new Error('User not found');
    }

    // Update user fields
    if (request.username !== undefined) userWithPassword.username = request.username;
    if (request.email !== undefined) userWithPassword.email = request.email;
    if (request.role !== undefined) userWithPassword.role = request.role;
    if (request.isActive !== undefined) userWithPassword.isActive = request.isActive;
    userWithPassword.updatedAt = new Date();

    this.users.set(id, userWithPassword);

    await securityLogService.log({
      type: 'user_updated',
      userId: id,
      ip: '127.0.0.1', // This should be passed from the request
      endpoint: `/api/auth/users/${id}`,
      method: 'PUT',
      statusCode: 200,
      message: `User ${userWithPassword.username} updated`,
    });

    const { password, ...user } = userWithPassword;
    return user;
  }

  /**
   * Record failed login attempt
   */
  private async recordFailedLogin(identifier: string, ip: string): Promise<void> {
    const attempts = this.loginAttempts.get(identifier) || { count: 0, lastAttempt: new Date() };
    attempts.count++;
    attempts.lastAttempt = new Date();

    // Lock account if too many attempts
    if (attempts.count >= securityConfig.security.maxLoginAttempts) {
      attempts.lockedUntil = new Date(Date.now() + securityConfig.security.lockoutDuration * 60 * 1000);
    }

    this.loginAttempts.set(identifier, attempts);

    await securityLogService.log({
      type: 'login_failed',
      ip,
      endpoint: '/api/auth/login',
      method: 'POST',
      statusCode: 401,
      message: `Failed login attempt for: ${identifier} (attempt ${attempts.count})`,
    });
  }
}

// Export singleton instance
export const authService = new AuthService();
