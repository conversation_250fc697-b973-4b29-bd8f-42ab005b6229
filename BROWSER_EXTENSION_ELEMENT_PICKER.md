# Browser Extension Element Picker Implementation Guide

## Overview

This document provides a comprehensive guide for implementing a browser extension that allows users to Ctrl+click on web elements to automatically generate selectors for the RPA application. The extension will communicate with the RPA app to send selected element information.

## Architecture Overview

```mermaid
graph TB
    subgraph "Browser Extension"
        A[Content Script] --> B[Background Script]
        B --> C[Popup UI]
    end
    
    subgraph "RPA Application"
        D[Frontend] --> E[Element Picker API]
        E --> F[AI Selector Generator]
    end
    
    A --> D
    F --> D
```

## Implementation Steps

### Step 1: Create Extension Manifest

Create `manifest.json` with Manifest V3 configuration:

```json
{
  "manifest_version": 3,
  "name": "RPA Element Picker",
  "version": "1.0.0",
  "description": "Pick web elements for RPA automation",
  "permissions": [
    "activeTab",
    "storage",
    "scripting"
  ],
  "host_permissions": [
    "<all_urls>"
  ],
  "content_scripts": [
    {
      "matches": ["<all_urls>"],
      "js": ["content.js"],
      "css": ["content.css"]
    }
  ],
  "background": {
    "service_worker": "background.js"
  },
  "action": {
    "default_popup": "popup.html",
    "default_title": "RPA Element Picker"
  },
  "web_accessible_resources": [
    {
      "resources": ["injected.js"],
      "matches": ["<all_urls>"]
    }
  ]
}
```

### Step 2: Content Script Implementation

Create `content.js` for element picking functionality:

```javascript
// content.js
class ElementPicker {
  constructor() {
    this.isActive = false;
    this.highlightedElement = null;
    this.overlay = null;
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Listen for messages from popup/background
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      switch (message.action) {
        case 'START_PICKING':
          this.startPicking();
          sendResponse({ success: true });
          break;
        case 'STOP_PICKING':
          this.stopPicking();
          sendResponse({ success: true });
          break;
        case 'GET_STATUS':
          sendResponse({ isActive: this.isActive });
          break;
      }
    });
  }

  startPicking() {
    if (this.isActive) return;
    
    this.isActive = true;
    this.createOverlay();
    this.addEventListeners();
    this.showInstructions();
  }

  stopPicking() {
    if (!this.isActive) return;
    
    this.isActive = false;
    this.removeEventListeners();
    this.removeOverlay();
    this.removeHighlight();
    this.hideInstructions();
  }

  createOverlay() {
    this.overlay = document.createElement('div');
    this.overlay.id = 'rpa-picker-overlay';
    this.overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(253, 116, 108, 0.1);
      z-index: 999999;
      pointer-events: none;
      border: 2px dashed #fd746c;
    `;
    document.body.appendChild(this.overlay);
  }

  addEventListeners() {
    document.addEventListener('mouseover', this.handleMouseOver.bind(this), true);
    document.addEventListener('mouseout', this.handleMouseOut.bind(this), true);
    document.addEventListener('click', this.handleClick.bind(this), true);
    document.addEventListener('keydown', this.handleKeyDown.bind(this), true);
  }

  removeEventListeners() {
    document.removeEventListener('mouseover', this.handleMouseOver, true);
    document.removeEventListener('mouseout', this.handleMouseOut, true);
    document.removeEventListener('click', this.handleClick, true);
    document.removeEventListener('keydown', this.handleKeyDown, true);
  }

  handleMouseOver(event) {
    if (!this.isActive) return;
    
    event.stopPropagation();
    this.highlightElement(event.target);
  }

  handleMouseOut(event) {
    if (!this.isActive) return;
    
    event.stopPropagation();
    this.removeHighlight();
  }

  handleClick(event) {
    if (!this.isActive) return;
    
    if (event.ctrlKey || event.metaKey) {
      event.preventDefault();
      event.stopPropagation();
      
      this.selectElement(event.target);
    }
  }

  handleKeyDown(event) {
    if (!this.isActive) return;
    
    if (event.key === 'Escape') {
      event.preventDefault();
      this.stopPicking();
    }
  }

  highlightElement(element) {
    this.removeHighlight();
    
    if (element === document.body || element === document.documentElement) {
      return;
    }
    
    this.highlightedElement = element;
    element.style.outline = '2px solid #fd746c';
    element.style.outlineOffset = '2px';
    element.style.backgroundColor = 'rgba(253, 116, 108, 0.1)';
  }

  removeHighlight() {
    if (this.highlightedElement) {
      this.highlightedElement.style.outline = '';
      this.highlightedElement.style.outlineOffset = '';
      this.highlightedElement.style.backgroundColor = '';
      this.highlightedElement = null;
    }
  }

  async selectElement(element) {
    const elementInfo = this.extractElementInfo(element);
    const selector = this.generateSelector(element);
    
    // Send to RPA application
    await this.sendToRPAApp({
      selector,
      elementInfo,
      url: window.location.href
    });
    
    this.stopPicking();
  }

  extractElementInfo(element) {
    return {
      tagName: element.tagName.toLowerCase(),
      id: element.id || null,
      className: element.className || null,
      textContent: element.textContent?.trim().substring(0, 100) || null,
      attributes: this.getRelevantAttributes(element),
      position: this.getElementPosition(element),
      size: this.getElementSize(element)
    };
  }

  getRelevantAttributes(element) {
    const relevantAttrs = ['name', 'type', 'value', 'placeholder', 'title', 'alt', 'href', 'src'];
    const attrs = {};
    
    relevantAttrs.forEach(attr => {
      if (element.hasAttribute(attr)) {
        attrs[attr] = element.getAttribute(attr);
      }
    });
    
    return attrs;
  }

  generateSelector(element) {
    // Priority order for selector generation
    const selectors = [
      this.generateIdSelector(element),
      this.generateDataAttributeSelector(element),
      this.generateNameSelector(element),
      this.generateClassSelector(element),
      this.generateTagSelector(element)
    ].filter(Boolean);
    
    return selectors[0] || this.generateXPathSelector(element);
  }

  generateIdSelector(element) {
    if (element.id && this.isUniqueSelector(`#${element.id}`)) {
      return `#${element.id}`;
    }
    return null;
  }

  generateDataAttributeSelector(element) {
    const dataAttrs = Array.from(element.attributes)
      .filter(attr => attr.name.startsWith('data-'))
      .filter(attr => !attr.value.match(/\d{10,}/)); // Avoid timestamps/IDs
    
    for (const attr of dataAttrs) {
      const selector = `[${attr.name}="${attr.value}"]`;
      if (this.isUniqueSelector(selector)) {
        return selector;
      }
    }
    return null;
  }

  isUniqueSelector(selector) {
    try {
      return document.querySelectorAll(selector).length === 1;
    } catch {
      return false;
    }
  }

  async sendToRPAApp(data) {
    // Method 1: PostMessage to RPA app (if in iframe or same origin)
    if (window.parent !== window) {
      window.parent.postMessage({
        type: 'RPA_ELEMENT_SELECTED',
        data
      }, '*');
    }
    
    // Method 2: LocalStorage communication
    localStorage.setItem('rpa_selected_element', JSON.stringify({
      ...data,
      timestamp: Date.now()
    }));
    
    // Method 3: Direct API call to RPA backend
    try {
      await fetch('http://localhost:3001/api/element-picker/receive', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      });
    } catch (error) {
      console.log('Could not reach RPA API directly');
    }
    
    // Notify background script
    chrome.runtime.sendMessage({
      action: 'ELEMENT_SELECTED',
      data
    });
  }

  showInstructions() {
    const instructions = document.createElement('div');
    instructions.id = 'rpa-picker-instructions';
    instructions.innerHTML = `
      <div style="position: fixed; top: 20px; left: 50%; transform: translateX(-50%);
                  background: #1a0f0f; color: #fbf9f8; padding: 12px 20px;
                  border-radius: 8px; z-index: 1000000; font-family: Arial, sans-serif;
                  box-shadow: 0 4px 12px rgba(0,0,0,0.3);">
        🎯 <strong>RPA Element Picker Aktiv</strong><br>
        <small>Ctrl+Klick för att välja element • Escape för att avbryta</small>
      </div>
    `;
    document.body.appendChild(instructions);
  }

  hideInstructions() {
    const instructions = document.getElementById('rpa-picker-instructions');
    if (instructions) {
      instructions.remove();
    }
  }
}

// Initialize element picker
const elementPicker = new ElementPicker();
```

### Step 3: Background Script

Create `background.js` for extension coordination:

```javascript
// background.js
class RPAExtensionBackground {
  constructor() {
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Handle extension icon click
    chrome.action.onClicked.addListener((tab) => {
      this.togglePicker(tab.id);
    });

    // Handle messages from content script
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      switch (message.action) {
        case 'ELEMENT_SELECTED':
          this.handleElementSelected(message.data, sender.tab);
          break;
      }
    });
  }

  async togglePicker(tabId) {
    try {
      // Check current status
      const response = await chrome.tabs.sendMessage(tabId, {
        action: 'GET_STATUS'
      });

      if (response.isActive) {
        await chrome.tabs.sendMessage(tabId, { action: 'STOP_PICKING' });
        chrome.action.setBadgeText({ text: '', tabId });
      } else {
        await chrome.tabs.sendMessage(tabId, { action: 'START_PICKING' });
        chrome.action.setBadgeText({ text: 'ON', tabId });
        chrome.action.setBadgeBackgroundColor({ color: '#fd746c', tabId });
      }
    } catch (error) {
      console.error('Could not communicate with content script:', error);
    }
  }

  handleElementSelected(data, tab) {
    // Store selected element data
    chrome.storage.local.set({
      lastSelectedElement: {
        ...data,
        tabId: tab.id,
        tabUrl: tab.url,
        timestamp: Date.now()
      }
    });

    // Reset badge
    chrome.action.setBadgeText({ text: '', tabId: tab.id });

    // Notify RPA application
    this.notifyRPAApplication(data);
  }

  async notifyRPAApplication(data) {
    // Try multiple communication methods
    
    // Method 1: Find RPA app tab and send message
    const tabs = await chrome.tabs.query({ url: 'http://localhost:3000/*' });
    if (tabs.length > 0) {
      chrome.tabs.sendMessage(tabs[0].id, {
        type: 'RPA_ELEMENT_SELECTED',
        data
      });
    }

    // Method 2: Use chrome.storage for cross-tab communication
    chrome.storage.local.set({
      pendingElementSelection: {
        ...data,
        timestamp: Date.now()
      }
    });
  }
}

// Initialize background script
new RPAExtensionBackground();
```

### Step 4: RPA Application Integration

#### Backend API Endpoint

Add to `backend/src/routes/elementPicker.ts`:

```typescript
import { Router, Request, Response } from 'express';

export const elementPickerRouter = Router();

// Store pending selections temporarily
const pendingSelections = new Map<string, any>();

elementPickerRouter.post('/receive', async (req: Request, res: Response) => {
  try {
    const { selector, elementInfo, url } = req.body;
    
    // Generate optimized selector using AI
    const optimizedSelector = await generateOptimalSelector(elementInfo, url);
    
    // Store for frontend to retrieve
    const selectionId = Date.now().toString();
    pendingSelections.set(selectionId, {
      originalSelector: selector,
      optimizedSelector,
      elementInfo,
      url,
      timestamp: Date.now()
    });
    
    res.json({ 
      success: true, 
      selectionId,
      selector: optimizedSelector 
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

elementPickerRouter.get('/pending/:selectionId', (req: Request, res: Response) => {
  const selection = pendingSelections.get(req.params.selectionId);
  if (selection) {
    pendingSelections.delete(req.params.selectionId);
    res.json(selection);
  } else {
    res.status(404).json({ error: 'Selection not found' });
  }
});

async function generateOptimalSelector(elementInfo: any, url: string): Promise<string> {
  // Use existing AI service to optimize selector
  const prompt = `
    Generate the most robust CSS selector for this element:
    
    Tag: ${elementInfo.tagName}
    ID: ${elementInfo.id}
    Classes: ${elementInfo.className}
    Attributes: ${JSON.stringify(elementInfo.attributes)}
    Text: ${elementInfo.textContent}
    
    Prioritize:
    1. Unique IDs
    2. Stable data attributes
    3. Semantic classes
    4. Structural selectors
    
    Avoid dynamic classes and deep paths.
    Return only the CSS selector.
  `;
  
  // Use your existing LLM service
  const response = await llmService.generateText(prompt);
  return response.trim();
}
```

#### Frontend Integration

Extend `ElementSelector.tsx`:

```typescript
// Add to ElementSelector component
const [isWaitingForPicker, setIsWaitingForPicker] = useState(false);

useEffect(() => {
  // Listen for element selections from extension
  const handleMessage = (event: MessageEvent) => {
    if (event.data.type === 'RPA_ELEMENT_SELECTED') {
      const { selector } = event.data.data;
      onChange(selector);
      setIsWaitingForPicker(false);
    }
  };

  window.addEventListener('message', handleMessage);
  return () => window.removeEventListener('message', handleMessage);
}, [onChange]);

const startElementPicker = () => {
  setIsWaitingForPicker(true);
  
  // Check if extension is available
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    // Communicate with extension
    chrome.runtime.sendMessage('extension-id', {
      action: 'START_PICKING'
    });
  } else {
    // Fallback: Show instructions to install extension
    alert('Browser extension krävs för element-picking funktionalitet');
    setIsWaitingForPicker(false);
  }
};
```

## Testing Instructions

1. **Load Extension in Chrome:**
   - Go to `chrome://extensions/`
   - Enable Developer mode
   - Click "Load unpacked" and select extension folder

2. **Test Element Picking:**
   - Navigate to any website
   - Click extension icon to activate picker
   - Ctrl+click on elements to select them
   - Verify selectors appear in RPA app

3. **Test Communication:**
   - Open RPA app in another tab
   - Verify selected elements appear in ElementSelector components

## Deployment Considerations

- Package extension for Chrome Web Store
- Add proper icons and descriptions
- Implement error handling and user feedback
- Add settings page for RPA app URL configuration
- Consider Firefox/Safari compatibility

## Security Notes

- Extension only activates on user action
- No automatic data collection
- Secure communication between extension and RPA app
- Validate all selector inputs in RPA backend
