import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '@rpa-project/shared';

// Simple authentication middleware for testing
export function simpleAuth(req: Request, res: Response, next: NextFunction): any {
  // For now, just check if there's an Authorization header
  const authHeader = req.headers.authorization;
  
  if (!authHeader) {
    const response: ApiResponse = {
      success: false,
      error: 'Authentication required. Please provide Authorization header.',
    };
    return res.status(401).json(response);
  }

  // Simple check - just verify it starts with "Bearer "
  if (!authHeader.startsWith('Bearer ')) {
    const response: ApiResponse = {
      success: false,
      error: 'Invalid authorization format. Use: Bearer <token>',
    };
    return res.status(401).json(response);
  }

  // For testing, accept any token that's not empty
  const token = authHeader.substring(7); // Remove "Bearer "
  if (!token || token.trim() === '') {
    const response: ApiResponse = {
      success: false,
      error: 'Empty token provided',
    };
    return res.status(401).json(response);
  }

  // Add a simple user context for testing
  (req as any).user = {
    id: 'test-user',
    username: 'test',
    role: 'admin'
  };

  next();
}

// Simple authorization middleware
export function simpleAuthorize(req: Request, res: Response, next: NextFunction): any {
  // For testing, just check if user exists (set by simpleAuth)
  if (!(req as any).user) {
    const response: ApiResponse = {
      success: false,
      error: 'User context not found',
    };
    return res.status(403).json(response);
  }

  next();
}

// Test token generator
export function generateTestToken(): string {
  return 'test-token-' + Date.now();
}

console.log('🔐 Simple auth middleware loaded');
console.log('📝 Test token example:', generateTestToken());
console.log('💡 Use: Authorization: Bearer test-token-123');
