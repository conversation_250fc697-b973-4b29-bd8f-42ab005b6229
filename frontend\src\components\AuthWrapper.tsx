import React, { useState, useEffect, ReactNode } from 'react';
import { authService, AuthState } from '../services/auth';
import { LoginPrompt } from './LoginPrompt';

interface AuthWrapperProps {
  children: ReactNode;
}

export const AuthWrapper: React.FC<AuthWrapperProps> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>(authService.getAuthState());
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Subscribe to auth state changes
    const unsubscribe = authService.subscribe(setAuthState);
    
    // Initial load is complete
    setIsLoading(false);
    
    return unsubscribe;
  }, []);

  const handleLogin = () => {
    // Force a re-render after login
    setAuthState(authService.getAuthState());
  };

  // Show loading spinner during initial load
  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#fbf9f8] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#fd746c] mx-auto"></div>
          <p className="mt-4 text-sm text-gray-600">Laddar...</p>
        </div>
      </div>
    );
  }

  // Show login prompt if not authenticated
  if (!authState.isAuthenticated) {
    return <LoginPrompt onLogin={handleLogin} />;
  }

  // Show main app if authenticated
  return <>{children}</>;
};

export default AuthWrapper;
