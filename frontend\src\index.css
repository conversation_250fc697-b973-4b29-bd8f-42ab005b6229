@import url('https://fonts.googleapis.com/css2?family=Work+Sans:wght@400;500;700;900&family=Noto+Sans:wght@400;500;700;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Work Sans', 'Noto Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #fbf9f8;
  color: #1a0f0f;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Layout */
.app-layout {
  display: flex;
  height: 100vh;
  background-color: #fbf9f8;
}

.sidebar {
  width: 280px;
  background: #fbf9f8;
  color: #1a0f0f;
  padding: 1rem;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
  border-right: 1px solid #e5d2d1;
}

.main-content {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fbf9f8;
}

.main-content.flow-editor {
  padding: 0;
}

/* Layout Content Container */
.layout-content-container {
  flex: 1;
  max-width: 960px;
  margin: 0 auto;
  width: 100%;
}

/* New Design System Classes */
.design-root {
  position: relative;
  display: flex;
  min-height: 100vh;
  flex-direction: column;
  background-color: #fbf9f8;
  font-family: 'Work Sans', 'Noto Sans', sans-serif;
  overflow-x: hidden;
}

.layout-container {
  display: flex;
  height: 100%;
  flex-grow: 1;
  flex-direction: column;
}

.layout-wrapper {
  gap: 0.25rem;
  display: flex;
  flex: 1;
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.sidebar-container {
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  width: 320px;
  height: 100vh;
  flex-shrink: 0;
  z-index: 100;
  overflow-y: auto;
}

.sidebar-content {
  display: flex;
  height: 100%;
  min-height: 700px;
  flex-direction: column;
  background-color: #fbf9f8;
  padding: 1rem;
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  flex: 1;
}

.sidebar-title {
  color: #1a0f0f;
  font-size: 1rem;
  font-weight: 500;
  line-height: normal;
  margin: 0;
}

.nav-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0.75rem;
  border-radius: 9999px;
  transition: all 0.2s;
  text-decoration: none;
  color: #1a0f0f;
  border: 1px solid transparent;
  margin-bottom: 0.25rem;
}

.nav-item:hover {
  background-color: #fef7f7;
  text-decoration: none;
  color: #1a0f0f;
}

.nav-item.active {
  background-color: #fef7f7;
  border-color: #fd746c;
}

.nav-icon {
  color: #1a0f0f;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon svg {
  width: 24px;
  height: 24px;
  fill: currentColor;
  display: block;
}

.nav-item:hover .nav-icon {
  color: #fd746c;
}

.nav-item.active .nav-icon {
  color: #fd746c;
}

.nav-label {
  color: #1a0f0f;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: normal;
  margin: 0;
}

.main-content-wrapper {
  display: flex;
  flex-direction: column;
  flex: 1;
  margin-left: calc(320px + 1rem);
  margin-right: 1.5rem;
}

.main-content-wrapper.flow-editor {
  margin-left: 0;
  margin-right: 0;
}

/* Dashboard Classes */
.dashboard-container {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.dashboard-header {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 0.75rem;
  padding: 1rem;
}

.dashboard-header-content {
  display: flex;
  min-width: 18rem;
  flex-direction: column;
  gap: 0.75rem;
}

.dashboard-title {
  color: #1a0f0f;
  font-size: 2rem;
  font-weight: 500;
  line-height: 1.2;
  margin: 0;
}

.dashboard-subtitle {
  font-size: 0.875rem;
  font-weight: normal;
  line-height: normal;
  margin: 0;
}

.section-subtitle {
  margin: 0.5rem 0 0 16px
}

.stats-grid {
  display: flex !important;
  flex-wrap: nowrap !important;
  gap: 1rem !important;
  padding: 1rem !important;
  overflow-x: auto !important;
  grid-template-columns: none !important;
  margin-bottom: 0 !important;
}

.stat-card {
  display: flex !important;
  min-width: 280px !important;
  max-width: 400px !important;
  flex-shrink: 0 !important;
  flex-direction: column !important;
  gap: 0.5rem !important;
  border-radius: 0.75rem !important;
  padding: 1.5rem !important;
  box-shadow: none !important;
  background-color: #ffffff !important;
  border: 1px solid #e5e7eb !important;
}

.stat-label {
  color: #1a0f0f;
  font-size: 1rem;
  font-weight: 500;
  line-height: normal;
  margin: 0;
}

.stat-value {
  color: #1a0f0f;
  font-size: 1.5rem;
  font-weight: bold;
  line-height: 1.2;
  margin: 0;
}

.stat-change {
  color: #16a34a;
  font-size: 1rem;
  font-weight: 500;
  line-height: normal;
  margin: 0;
}

.section-title {
  color: #1a0f0f;
  font-size: 1.375rem;
  font-weight: 500;
  line-height: 1.2;
  letter-spacing: -0.015em;
  padding: 1rem 1rem 0.75rem 1rem;
  margin: 0;
}

.table-container {
  padding: 0 1rem 0.75rem 1rem;
}

.activity-table {
  display: flex;
  overflow: hidden;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  background-color: white;
}

.table {
  flex: 1;
  border-collapse: collapse;
}

.table thead tr {
  background-color: white !important;
}

.table thead {
  background-color: white !important;
}

.table th {
  background-color: white !important;
}

.activity-table thead tr {
  background-color: white !important;
}

.activity-table th {
  background-color: white !important;
}

.table th {
  padding: 0.75rem 1rem;
  text-align: left;
  color: #1a0f0f;
  font-size: 0.875rem;
  font-weight: 500 !important;
  line-height: normal;
}

.table th:first-child {
  width: 30%;
}

.table th:nth-child(2) {
  width: 15%;
}

.table th:nth-child(3) {
  width: 25%;
}

.table th:nth-child(4) {
  width: 30%;
}

.table tbody tr {
  border-top: 1px solid #e5e7eb;
}

.table td {
  height: 72px;
  padding: 0.5rem 1rem;
  color: #1a0f0f;
  font-size: 0.875rem;
  font-weight: normal;
  line-height: normal;
}

.table td.secondary-text {
  color: #6b7280;
}

.status-button {
  display: flex;
  min-width: 84px;
  max-width: 120px;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 9999px;
  height: 2rem;
  padding: 0 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: normal;
  border: none;
}

.status-completed {
  background-color: #e8f5e8;
  color: #1a0f0f;
}

.status-running {
  background-color: #fff3e0;
  color: #1a0f0f;
}

.status-scheduled {
  background-color: #e3f2fd;
  color: #1a0f0f;
}

.status-failed {
  background-color: #ffebee;
  color: #1a0f0f;
}

.quick-actions {
  display: flex;
  justify-content: stretch;
}

.quick-actions-content {
  display: flex;
  flex: 1;
  gap: 0.75rem;
  flex-wrap: wrap;
  padding: 0.75rem 1rem;
  justify-content: flex-start;
}

/* Page Layout Classes */
.page-container {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.page-header {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
}

.page-header-content {
  display: flex;
  min-width: 18rem;
  flex-direction: column;
  gap: 0.75rem;
}

.page-title {
  color: #1a0f0f;
  font-size: 2rem;
  font-weight: bold;
  line-height: 1.2;
  margin: 0;
}

.page-subtitle {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: normal;
  line-height: normal;
  margin: 0;
}

.page-subtitle-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

/* Info Cards */
.info-grid {
  display: flex;
  flex-wrap: nowrap;
  gap: 1rem;
  padding: 1rem;
  overflow-x: auto;
}

.info-card {
  display: flex;
  min-width: 200px;
  max-width: 300px;
  flex-shrink: 0;
  flex-direction: column;
  gap: 0.5rem;
  border-radius: 0.75rem;
  padding: 1.5rem;
  background-color: #fef7f7;
  border: 1px solid #f2e8e8;
}

.info-label {
  color: #1a0f0f;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: normal;
  margin: 0;
}

.info-value {
  color: #1a0f0f;
  font-size: 1.5rem;
  font-weight: bold;
  line-height: 1.2;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Enhanced Cards */
.card-enhanced {
  background-color: white;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.card-enhanced-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.card-enhanced-title {
  color: #1a0f0f;
  font-size: 1.375rem;
  font-weight: bold;
  line-height: 1.2;
  margin: 0;
}

/* Flow Item */
.flow-item {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.flow-item:last-child {
  border-bottom: none;
}

.flow-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
}

.flow-content {
  flex: 1;
}

.flow-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a0f0f;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.flow-description {
  color: #6b7280;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.flow-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.flow-meta-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.flow-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 1.5rem;
}

.empty-state-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state-title {
  color: #1a0f0f;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.empty-state-subtitle {
  color: #6b7280;
  margin: 0 0 1.5rem 0;
}

/* Error Card */
.error-card {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.error-title {
  color: #dc2626;
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.error-message {
  color: #dc2626;
  margin: 0;
}

/* Action Buttons */
.action-button {
  display: flex;
  min-width: 84px;
  max-width: 480px;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 9999px;
  height: 2.5rem;
  padding: 0 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  line-height: normal;
  text-decoration: none;
  border: none;
  transition: all 0.2s;
}

.action-button.primary {
  background-color: #fd746c;
  color: white;
}

.action-button.primary:hover {
  background-color: #fc5a50;
}

.action-button.secondary {
  background-color: #fef7f7;
  color: #1a0f0f;
  border: 1px solid #fd746c;
}

.action-button.secondary:hover {
  background-color: #fdeae8;
}

.action-button.danger {
  background-color: #dc2626;
  color: white;
}

.action-button.danger:hover {
  background-color: #b91c1c;
}

.action-button.centered {
  display: flex;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  justify-content: center
}

/* Search Input */
.search-input {
  padding: 0 1rem;
  border: 1px solid #d1d5db;
  border-radius: 9999px;
  font-size: 0.875rem;
  height: 2.5rem;
  width: 300px;
  background-color: #ffffff;
  outline: none;
  transition: border-color 0.2s;
}

.search-input:focus {
  border-color: #fd746c;
}

/* Utility Classes */
.secondary-text {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Flow Table Specific */
.flow-name {
  font-weight: 500;
  color: #1a0f0f;
  margin-bottom: 0.25rem;
}

.flow-name-link {
  color: #fd746c;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.flow-name-link:hover {
  color: #e85d54;
  text-decoration: underline;
}

.flow-description-small {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

.flow-link {
  display: block;
  text-decoration: none;
  color: inherit;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0.25rem 0;
  border-radius: 0.375rem;
}

.flow-link:hover {
  background-color: #f8fafc;
  transform: translateX(2px);
}

.flow-link:hover .flow-name {
  color: #fd746c;
}

.flow-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

/* Filter Panel */
.filter-panel {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
}

.filter-panel-buttons {
  display: flex;
  gap: 0.5rem;
}



/* Choice Buttons (Radio Button Style) */
.choice-buttons {
  display: flex;
  gap: 1rem;
}

.choice-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 100px;
  background-color: #ffffff;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.choice-button:hover {
  border-color: #fd746c;
  background-color: #fef7f7;
}

.choice-button.selected {
  border-color: #fd746c;
  background-color: #fd746c;
  color: #ffffff;
}

.choice-button input[type="radio"] {
  display: none;
}

.action-button-small {
  display: flex;
  min-width: 60px;
  max-width: 100px;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 9999px;
  height: 2rem;
  padding: 0 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: normal;
  text-decoration: none;
  border: none;
  transition: all 0.2s;
}

.action-button-small.primary {
  background-color: #fd746c;
  color: white;
}

.action-button-small.primary:hover {
  background-color: #fc5a50;
}

.action-button-small.secondary {
  background-color: #fef7f7;
  color: #1a0f0f;
  border: 1px solid #fd746c;
}

.action-button-small.secondary:hover {
  background-color: #fdeae8;
}

.action-button-small.danger {
  background-color: transparent;
  border: 1px solid #e5e7eb;
  color: #dc2626;
}

.action-button-small.danger:hover {
  background-color: #b91c1c;
  color: #ffffff;
}

.action-button-small.icon-only {
  background-color: transparent;
  border: 1px solid #e5e7eb;
  color: #37513e;
  width: 60px;
  padding: 0;
}

.action-button-small.icon-only:hover {
  background-color: #f9fafb;
  border-color: #fd746c;
  color: #fd746c;
}

.action-button-small.icon-only svg {
  width: 16px;
  height: 16px;
}

/* Empty State Container */
.empty-state-container {
  padding: 1rem;
}

.empty-state {
  text-align: center;
  padding: 3rem 1.5rem;
  background-color: white;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.action-button {
  display: flex;
  min-width: 84px;
  max-width: 480px;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 9999px;
  height: 2.5rem;
  padding: 0 1rem;
  font-size: 0.875rem;
  font-weight: bold;
  line-height: normal;
  letter-spacing: 0.015em;
  text-decoration: none;
  transition: all 0.2s;
  border: none;
}

.action-button.primary {
  background-color: #fd746c;
  color: white;
}

.action-button.primary:hover {
  background-color: #fc5a50;
}

.action-button.secondary {
  background-color: #fef7f7;
  color: #1a0f0f;
}

.action-button.secondary:hover {
  background-color: #fdeae8;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 16rem;
}

.loading-text {
  color: #6b7280;
  font-size: 1.125rem;
}

/* Navigation */
.nav-link {
  display: block;
  padding: 0.75rem 1rem;
  color: #cbd5e1;
  text-decoration: none;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
  transition: all 0.2s;
}

.nav-link:hover {
  background: #334155;
  color: white;
}

.nav-link.active {
  background: #3b82f6;
  color: white;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 2rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.btn-primary {
  background: #fd746c;
  color: white;
}

.btn-primary:hover {
  background: #fc5a50;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background: #4b5563;
}

.btn-success {
  background: #16a34a;
  color: white;
}

.btn-success:hover {
  background: #15803d;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
}

.btn-outline {
  background: transparent;
  border: 1px solid #e5d2d1;
  color: #1a0f0f;
}

.btn-outline:hover {
  background: #fef7f7;
}

/* Cards */
.card {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.card-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

/* Forms */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a0f0f;
  margin-bottom: 0.5rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background-color: #ffffff;
  transition: all 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #fd746c;
  box-shadow: 0 0 0 3px rgba(253, 116, 108, 0.1);
}

.form-display-value {
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  color: #1a0f0f;
  min-height: 2.75rem;
  display: flex;
  align-items: center;
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Ensure all select elements use consistent styling */
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Responsive Form Grid */
.responsive-form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

@media (max-width: 640px) {
  .responsive-form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (min-width: 768px) {
  .responsive-form-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  }
}

@media (min-width: 1024px) {
  .responsive-form-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

/* Form Container */
.form-container {
  overflow: hidden;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  background-color: white;
}

/* Tables */
.table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table th,
.table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.table tr:hover {
  background: #f9fafb;
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-pending {
  background: #fef3c7;
  color: #92400e;
}

.status-running {
  background: #dbeafe;
  color: #1e40af;
}

.status-completed {
  background: #d1fae5;
  color: #065f46;
}

.status-failed {
  background: #fee2e2;
  color: #991b1b;
}

.status-cancelled {
  background: #f3f4f6;
  color: #374151;
}

/* Loading */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* React Flow overrides */
.react-flow {
  background: #f8fafc;
}

.react-flow__node {
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.react-flow__node.selected {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.react-flow__handle {
  width: 12px;
  height: 12px;
  background: #6b7280;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.react-flow__handle.react-flow__handle-top,
.react-flow__handle.react-flow__handle-bottom {
  border-radius: 50%;
}

.react-flow__edge-path {
  stroke: #6b7280;
  stroke-width: 2;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #3b82f6;
  stroke-width: 3;
}

.react-flow__edge:hover .react-flow__edge-path {
  stroke: #374151;
}

/* React Flow Controls styling */
.react-flow__controls {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.react-flow__controls-button {
  background: white;
  border: none;
  border-bottom: 1px solid #e5e7eb;
  color: #374151;
  transition: all 0.2s;
}

.react-flow__controls-button:hover {
  background: #f9fafb;
  color: #111827;
}

.react-flow__controls-button:last-child {
  border-bottom: none;
}

/* React Flow MiniMap styling */
.react-flow__minimap {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.react-flow__minimap-mask {
  fill: rgba(59, 130, 246, 0.1);
  stroke: #3b82f6;
  stroke-width: 2;
}

/* Flow Designer Container */
.flow-designer-container {
  height: 100%;
  width: 100%;
}

/* Page Layout */
.page-container {
  width: 100%;
  padding: 2rem;
  background-color: #f8fafc;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.page-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.page-subtitle {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.page-subtitle-item {
  color: #6b7280;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Enhanced Cards */
.card-enhanced {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #e5e7eb;
}

.card-enhanced-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.card-enhanced-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Status System */
.status-badge-enhanced {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  border-radius: 9999px;
  border: 1px solid;
  gap: 0.5rem;
}

.status-completed {
  background-color: #d1fae5;
  color: #065f46;
  border-color: #a7f3d0;
}

.status-running {
  background-color: #dbeafe;
  color: #1e40af;
  border-color: #bfdbfe;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
  border-color: #fed7aa;
}

.status-failed {
  background-color: #fee2e2;
  color: #991b1b;
  border-color: #fecaca;
}

.status-cancelled {
  background-color: #f3f4f6;
  color: #374151;
  border-color: #e5e7eb;
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.info-card {
  padding: 1.5rem;
  background-color: #ffffff;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.info-label {
  display: block;
  font-size: 0.75rem;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.5rem;
}

.info-value {
  font-size: 0.875rem;
  color: #1f2937;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Error Display */
.error-card {
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.5rem;
  border-left: 4px solid #ef4444;
}

.error-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #991b1b;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.error-message {
  font-size: 0.875rem;
  color: #7f1d1d;
  line-height: 1.5;
  font-family: Monaco, Consolas, "Courier New", monospace;
}

/* Code Display */
.code-container {
  background-color: #f8fafc;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  position: relative;
}

.code-label {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  font-size: 0.75rem;
  color: #6b7280;
  background-color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  border: 1px solid #d1d5db;
}

.code-content {
  font-size: 0.875rem;
  color: #1f2937;
  overflow-x: auto;
  margin: 0;
  line-height: 1.5;
  font-family: Monaco, Consolas, "Courier New", monospace;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Enhanced Table */
.table-enhanced {
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-enhanced table {
  width: 100%;
  border-collapse: collapse;
}

.table-enhanced thead tr {
  background-color: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.table-enhanced th {
  padding: 0.75rem 1rem;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table-enhanced tbody tr {
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
}

.table-enhanced tbody tr:last-child {
  border-bottom: none;
}

.table-enhanced td {
  padding: 0.75rem 1rem;
  white-space: nowrap;
}

/* Log Level Styles */
.log-row-error {
  border-left: 4px solid #ef4444;
  background-color: #fef2f2;
}

.log-row-error:hover {
  background-color: #fee2e2;
}

.log-row-warn {
  border-left: 4px solid #f59e0b;
  background-color: #fffbeb;
}

.log-row-warn:hover {
  background-color: #fef3c7;
}

.log-row-info {
  border-left: 4px solid #3b82f6;
  background-color: #eff6ff;
}

.log-row-info:hover {
  background-color: #dbeafe;
}

.log-row-debug {
  border-left: 4px solid #6b7280;
  background-color: #f9fafb;
}

.log-row-debug:hover {
  background-color: #f3f4f6;
}

/* Log Level Badges */
.log-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 9999px;
  border: 1px solid;
}

.log-badge-error {
  background-color: #fee2e2;
  color: #991b1b;
  border-color: #fecaca;
}

.log-badge-warn {
  background-color: #fef3c7;
  color: #92400e;
  border-color: #fed7aa;
}

.log-badge-info {
  background-color: #dbeafe;
  color: #1e40af;
  border-color: #bfdbfe;
}

.log-badge-debug {
  background-color: #f3f4f6;
  color: #374151;
  border-color: #e5e7eb;
}

/* Utilities */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.text-sm {
  font-size: 0.875rem;
}

.text-gray-500 {
  color: #6b7280;
}

.text-red-500 {
  color: #ef4444;
}

.text-green-500 {
  color: #10b981;
}

.font-mono {
  font-family: Monaco, Consolas, "Courier New", monospace;
}

.step-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-family: Monaco, Consolas, "Courier New", monospace;
  background-color: #f3f4f6;
  color: #374151;
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
}

.retry-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 600;
  background-color: #fff3e0;
  color: #ea580c;
  border-radius: 9999px;
  border: 1px solid #fed7aa;
  gap: 0.25rem;
}

/* Data popup container - needs relative positioning */
.data-popup-container {
  position: relative;
  display: inline-block;
}

.data-popup {
  position: absolute;
  right: 0;
  top: 100%;
  z-index: 1000;
  margin-top: 0.25rem;
  padding: 0.75rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  min-width: 300px;
  max-width: 500px;
  max-height: 200px;
  overflow: auto;
}

.data-popup pre {
  font-size: 0.75rem;
  background-color: #f9fafb;
  padding: 0.5rem;
  border-radius: 0.375rem;
  overflow-x: auto;
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.data-link {
  cursor: pointer;
  color: #3b82f6;
  font-size: 0.75rem;
  font-weight: 500;
  list-style: none;
  text-decoration: none;
  border: none;
  background: none;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.data-link:hover {
  color: #2563eb;
  background-color: #f0f9ff;
}

.data-link::-webkit-details-marker {
  display: none;
}

.empty-state {
  text-align: center;
  padding: 3rem 0;
}

.empty-state-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state-title {
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.empty-state-subtitle {
  color: #9ca3af;
  font-size: 0.875rem;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.btn-small {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  border-radius: 0.375rem;
}

/* Stats Cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.stat-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: 0.25rem;
}

/* Enhanced Table Row Hover */
.table-enhanced tbody tr:hover {
  background-color: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Flow ID Link */
.flow-id-link {
  color: #3b82f6;
  text-decoration: none;
  font-family: Monaco, Consolas, "Courier New", monospace;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  background-color: #eff6ff;
  border-radius: 0.375rem;
  border: 1px solid #bfdbfe;
  transition: all 0.2s;
}

.flow-id-link:hover {
  color: #1e40af;
  background-color: #dbeafe;
  border-color: #93c5fd;
}

/* Refresh Button */
.refresh-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: #f8fafc;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.refresh-btn:hover:not(:disabled) {
  background-color: #f1f5f9;
  border-color: #9ca3af;
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Loading State */
.loading-overlay {
  position: relative;
}

.loading-overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.75rem;
}

/* Error Alert */
.error-alert {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-left: 4px solid #ef4444;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.error-alert-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #991b1b;
  font-weight: 500;
}

/* Modal Styles */
.modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 1000 !important;
  padding: 1rem !important;
  backdrop-filter: blur(2px);
}

.modal-content {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-height: 90vh;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin: 0;
  animation: modalSlideIn 0.2s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes expandStep {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    max-height: 1000px;
    transform: translateY(0);
  }
}

/* Custom Scrollbar Styles */
.custom-scrollbar {
  /* Firefox */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
  position: relative;
}



/* Page Transition Styles */
.page-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: visible;
}

.page-content {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.15s ease-out, transform 0.15s ease-out;
  will-change: opacity, transform;
}

.page-content.page-entering {
  opacity: 0;
  transform: translateY(8px);
}

.page-content.page-entered {
  opacity: 1;
  transform: translateY(0);
}

/* Main content wrapper */
.main-content-wrapper {
  overflow: visible;
}

/* Webkit browsers (Chrome, Safari, Edge) */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Schedule Indicator Styles */
.schedule-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid;
  transition: all 0.2s ease;
}

.schedule-indicator.active {
  background-color: #d1fae5;
  color: #065f46;
  border-color: #a7f3d0;
}

.schedule-indicator.inactive {
  background-color: #f3f4f6;
  color: #6b7280;
  border-color: #e5e7eb;
}

/* Login Page Styles - Following Design System */

/* Main login page container */
.login-page {
  min-height: 100vh;
  background-color: #fbf9f8;
  font-family: 'Work Sans', 'Noto Sans', sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-container {
  display: flex;
  width: 100%;
  max-width: 1200px;
  height: 700px;
  background-color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 40px;
  border-radius: 20px;
}

/* Left side - Login form */
.login-form-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: #ffffff;
}

.login-form-container {
  width: 100%;
  max-width: 400px;
}

/* Login header */
.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.login-logo-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #fd746c 0%, #fc5a50 100%);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.login-logo-icon svg {
  width: 1.75rem;
  height: 1.75rem;
}

.login-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1a0f0f;
  margin: 0;
}

.login-subtitle h2 {
  font-size: 1.375rem;
  font-weight: 500;
  color: #1a0f0f;
  margin: 0 0 0.5rem 0;
}

.login-subtitle p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

/* Login form */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.login-form-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Input containers */
.login-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.login-input-icon {
  position: absolute;
  left: 0.75rem;
  z-index: 1;
  color: #6b7280;
  width: 1.25rem;
  height: 1.25rem;
}

.login-input-icon svg {
  width: 100%;
  height: 100%;
}

.login-input {
  padding-left: 2.75rem !important;
  transition: all 0.2s ease;
}

.login-input:focus {
  border-color: #fd746c;
  box-shadow: 0 0 0 3px rgba(253, 116, 108, 0.1);
}

/* Error message */
.login-error {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.5rem;
  color: #dc2626;
}

.login-error-icon {
  flex-shrink: 0;
  width: 1.25rem;
  height: 1.25rem;
  color: #dc2626;
}

.login-error-icon svg {
  width: 100%;
  height: 100%;
}

.login-error p {
  font-size: 0.875rem;
  margin: 0;
}

/* Form actions */
.login-form-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.login-submit-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.login-quick-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.login-button-icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* Loading spinner */
.login-loading-spinner {
  width: 1.25rem;
  height: 1.25rem;
  animation: spin 1s linear infinite;
}

.login-loading-spinner svg {
  width: 100%;
  height: 100%;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Divider */
.login-divider {
  position: relative;
  text-align: center;
  margin: 0.5rem 0;
}

.login-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #e5e7eb;
}

.login-divider span {
  background-color: #ffffff;
  padding: 0 1rem;
  font-size: 0.875rem;
  color: #6b7280;
}

/* Right side - Branding */
.login-branding-section {
  flex: 1;
  position: relative;
  background: linear-gradient(135deg, #fd746c 0%, #fc5a50 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  overflow: hidden;
  border-radius: 0px 20px 20px 0px;
}

.login-branding-container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 500px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.login-branding-content {
  margin-bottom: 3rem;
}

.login-branding-header h1 {
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  line-height: 1.1;
}

.login-branding-header p {
  font-size: 1.25rem;
  opacity: 0.9;
  margin: 0 0 2rem 0;
  line-height: 1.5;
}

/* Features list */
.login-features {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.login-feature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.login-feature-icon {
  width: 2rem;
  height: 2rem;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.login-feature-icon svg {
  width: 1.25rem;
  height: 1.25rem;
}

.login-feature span {
  font-size: 1.125rem;
  font-weight: 500;
}

/* Testimonial */
.login-testimonial {
  margin-top: auto;
}

.login-testimonial-content {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
}

.login-testimonial-content p {
  font-size: 0.875rem;
  opacity: 0.9;
  margin: 0 0 0.75rem 0;
  font-style: italic;
  line-height: 1.6;
}

.login-testimonial-content cite {
  font-size: 0.875rem;
  font-weight: 600;
  font-style: normal;
}

/* Decorative elements */
.login-branding-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.login-decoration {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
}

.login-decoration-1 {
  width: 8rem;
  height: 8rem;
  top: 5rem;
  right: 5rem;
}

.login-decoration-2 {
  width: 5rem;
  height: 5rem;
  bottom: 5rem;
  right: 8rem;
}

.login-decoration-3 {
  width: 4rem;
  height: 4rem;
  top: 50%;
  right: 2.5rem;
  transform: translateY(-50%);
}

/* Security info */
.login-security-info {
  margin-top: 2rem;
  padding: 1rem;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
}

.login-security-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.login-security-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #3b82f6;
}

.login-security-icon svg {
  width: 100%;
  height: 100%;
}

.login-security-header h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a0f0f;
  margin: 0;
}

.login-security-features {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.login-security-feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.login-check-icon {
  width: 0.875rem;
  height: 0.875rem;
  color: #10b981;
}

/* Default credentials warning */
.login-default-credentials {
  padding: 0.75rem;
  background-color: #fffbeb;
  border: 1px solid #fde68a;
  border-radius: 0.375rem;
  margin-top: 0.75rem;
}

.login-warning-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.login-warning-icon {
  width: 1rem;
  height: 1rem;
  color: #d97706;
}

.login-warning-header span {
  font-size: 0.75rem;
  font-weight: 600;
  color: #92400e;
}

.login-credentials {
  font-size: 0.75rem;
  color: #92400e;
}

.login-credentials div {
  margin-bottom: 0.25rem;
}

.login-credentials code {
  background-color: #fef3c7;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.7rem;
  font-family: Monaco, Consolas, "Courier New", monospace;
}

.login-warning-text {
  font-weight: 600;
  margin-top: 0.5rem;
}

/* Footer */
.login-footer {
  margin-top: 2rem;
  text-align: center;
}

.login-footer-badges {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.login-footer-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.login-footer-badge svg {
  width: 0.875rem;
  height: 0.875rem;
}

.login-copyright {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0;
}

/* Responsive design */
@media (max-width: 1024px) {
  .login-container {
    flex-direction: column;
  }

  .login-branding-section {
    order: -1;
    min-height: 200px;
    flex: none;
  }

  .login-branding-header h1 {
    font-size: 2rem;
  }

  .login-branding-header p {
    font-size: 1rem;
  }

  .login-features {
    display: none;
  }

  .login-testimonial {
    margin-top: 1rem;
  }
}

@media (max-width: 640px) {
  .login-form-section {
    padding: 1rem;
  }

  .login-branding-section {
    padding: 1rem;
  }

  .login-footer-badges {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* Sidebar User Menu Styles */
.sidebar-user-menu {
  padding-top: 1rem;
  border-top: 1px solid #e5d2d1;
}

/* Bottom User Menu Styles - positioned at bottom of page in left area */
.bottom-user-menu {
  position: fixed;
  bottom: 1rem;
  left: 1rem;
  z-index: 1000;
  width: 288px; /* Same width as sidebar minus padding */
}



.sidebar-user-button {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background-color: transparent;
  border: none;
  border-radius: 9999px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #1a0f0f;
}

.sidebar-user-button:hover {
  background-color: #fef7f7;
}

.sidebar-user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.sidebar-user-avatar {
  width: 2rem;
  height: 2rem;
  background-color: #fd746c;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.sidebar-user-avatar svg {
  width: 1.25rem;
  height: 1.25rem;
}

.sidebar-user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 0;
}

.sidebar-user-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a0f0f;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.sidebar-user-role {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: capitalize;
}

/* User popup menu */
.sidebar-user-popup {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  margin-bottom: 0.5rem;
  z-index: 1000;
}

.sidebar-user-popup-content {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin: 0 0.75rem;
}

.sidebar-user-popup-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
}

.sidebar-user-popup-avatar {
  width: 2.5rem;
  height: 2.5rem;
  background-color: #fd746c;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.sidebar-user-popup-avatar svg {
  width: 1.5rem;
  height: 1.5rem;
}

.sidebar-user-popup-info {
  flex: 1;
  min-width: 0;
}

.sidebar-user-popup-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a0f0f;
  margin-bottom: 0.25rem;
}

.sidebar-user-popup-role {
  font-size: 0.75rem;
  padding: 0.125rem 0.5rem;
  border-radius: 9999px;
  text-transform: capitalize;
  font-weight: 500;
}

.sidebar-user-popup-section {
  margin-bottom: 0.75rem;
}

.sidebar-user-popup-section:last-of-type {
  margin-bottom: 1rem;
}

.sidebar-user-popup-label {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.sidebar-user-popup-token {
  font-family: Monaco, Consolas, "Courier New", monospace;
  font-size: 0.75rem;
  background-color: #f8fafc;
  padding: 0.5rem;
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
  word-break: break-all;
  color: #1a0f0f;
}

.sidebar-user-popup-permissions {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.sidebar-user-popup-permission {
  font-size: 0.75rem;
  font-weight: 500;
}

.sidebar-user-popup-permission.admin {
  color: #dc2626;
}

.sidebar-user-popup-permission.operator {
  color: #2563eb;
}

.sidebar-user-popup-permission.viewer {
  color: #059669;
}

.sidebar-user-popup-footer {
  padding-top: 0.75rem;
  border-top: 1px solid #e5e7eb;
}

.sidebar-logout-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #dc2626;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
}

.sidebar-logout-button:hover {
  background-color: #fee2e2;
  border-color: #fca5a5;
}

.sidebar-logout-button svg {
  width: 1rem;
  height: 1rem;
}

.login-fade-in {
  animation: fadeInUp 0.6s ease-out;
}

.login-fade-in-delay {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

/* Floating elements animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.login-float {
  animation: float 6s ease-in-out infinite;
}

.login-float-delay {
  animation: float 6s ease-in-out infinite 2s;
}

/* Enhanced input styling for login */
.login-input {
  transition: all 0.3s ease;
  border: 2px solid #e5e7eb;
}

.login-input:focus {
  border-color: #fd746c;
  box-shadow: 0 0 0 3px rgba(253, 116, 108, 0.1);
  transform: translateY(-1px);
}

.login-input:hover {
  border-color: #d1d5db;
}

/* Login button enhancements */
.login-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(253, 116, 108, 0.3);
}

.login-button:active {
  transform: translateY(0);
}

/* Decorative elements */
.login-decoration {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 8s ease-in-out infinite;
}

.login-decoration:nth-child(2) {
  animation-delay: -2s;
}

.login-decoration:nth-child(3) {
  animation-delay: -4s;
}

/* Mobile responsiveness for login */
@media (max-width: 1024px) {
  .design-root {
    background: linear-gradient(135deg, #fbf9f8 0%, #f8f6f5 50%, #f5f2f1 100%);
  }

  .login-fade-in-delay {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 1rem;
    padding: 2rem;
    margin: 1rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
}

/* Error message styling */
.login-error {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}
