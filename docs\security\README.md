# Säkerhetsguide för RPA-applikationen

## Översikt

RPA-applikationen har implementerat en omfattande säkerhetslösning som skyddar alla interna API:er. Detta dokument beskriver säkerhetsarkitekturen och hur du använder den säkert.

## 🔒 Säkerhetsfunktioner

### ✅ Implementerat

- **JWT-baserad autentisering** - Säkra access och refresh tokens
- **API-nyckel system** - För maskin-till-maskin kommunikation  
- **Rollbaserad åtkomstkontroll (RBAC)** - Granulära behörigheter
- **Rate limiting** - Skydd mot DoS och brute force
- **Input sanitization** - XSS och injection-skydd
- **CSRF-skydd** - Cross-site request forgery prevention
- **Säkerhetsövervakning** - Real-time monitoring och alerts
- **Säkerhetsloggar** - Komplett audit trail
- **Suspicious activity detection** - Automatisk hotidentifiering

### 🛡️ Säkerhetsnivåer

1. **Nätverkssäkerhet** - HTTPS, CORS, säkerhetsheaders
2. **Autentisering** - JWT tokens och API-nycklar
3. **Auktorisering** - Rollbaserade behörigheter
4. **Input-validering** - Sanitization och validering
5. **Övervakning** - Kontinuerlig säkerhetsanalys

## 🚀 Snabbstart

### 1. Första inloggning

```bash
# Standard admin-användare (ändra lösenordet omedelbart!)
Username: admin
Password: admin123!
```

### 2. Ändra admin-lösenord

```bash
POST /api/auth/change-password
Authorization: Bearer <your-token>
{
  "currentPassword": "admin123!",
  "newPassword": "YourSecurePassword123!"
}
```

### 3. Skapa nya användare

```bash
POST /api/auth/users
Authorization: Bearer <admin-token>
{
  "username": "operator1",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "role": "operator"
}
```

### 4. Skapa API-nycklar

```bash
POST /api/auth/api-keys
Authorization: Bearer <admin-token>
{
  "name": "Integration API",
  "description": "För automatiserad integration",
  "role": "api"
}
```

## 👥 Roller och behörigheter

### Admin
- **Full åtkomst** till alla funktioner
- Kan hantera användare och API-nycklar
- Tillgång till säkerhetsloggar och övervakning
- Kan ändra systeminställningar

### Operator  
- Kan skapa, redigera och köra RPA-flöden
- Kan hantera kunder och credentials
- Kan se execution-resultat och scheman
- Kan inte hantera användare eller säkerhet

### Viewer
- **Endast läsåtkomst** till flöden och data
- Kan se execution-resultat
- Kan inte skapa eller ändra något
- Kan inte köra flöden

### API
- **Begränsad åtkomst** för automatiserade system
- Kan läsa flöden och köra dem
- Kan se execution-resultat
- Kan inte ändra konfiguration

## 🔑 Autentisering

### JWT Tokens

```bash
# Logga in
POST /api/auth/login
{
  "username": "your-username",
  "password": "your-password"
}

# Använd token
GET /api/flows
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...

# Förnya token
POST /api/auth/refresh
{
  "refreshToken": "your-refresh-token"
}
```

### API-nycklar

```bash
# Använd API-nyckel
GET /api/flows
X-API-Key: rpa_api_abc123...
```

## ⚡ Rate Limiting

### Begränsningar

- **Global**: 1000 requests per 15 minuter
- **Autentisering**: 10 försök per 15 minuter  
- **API**: 100 requests per minut

### Headers

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 2024-01-01T12:00:00Z
```

## 📊 Säkerhetsövervakning

### Dashboard

```bash
GET /api/auth/security/dashboard
```

Visar:
- Säkerhetsmetriker
- Aktiva varningar  
- Senaste aktivitet
- Säkerhetshälsa

### Varningar

```bash
# Alla varningar
GET /api/auth/security/alerts

# Endast kritiska
GET /api/auth/security/alerts?type=high

# Bekräfta varning
POST /api/auth/security/alerts/{id}/acknowledge
```

### Säkerhetsloggar

```bash
# Senaste loggar
GET /api/auth/security/logs

# Filtrera på typ
GET /api/auth/security/logs?type=login_failed

# Filtrera på användare
GET /api/auth/security/logs?userId=user_123
```

## 🛠️ Konfiguration

### Miljövariabler

```bash
# JWT-säkerhet
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
AUTH_RATE_LIMIT_MAX_REQUESTS=10

# Säkerhetsinställningar
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=30
SESSION_TIMEOUT=1440

# CORS och origins
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
IP_WHITELIST=127.0.0.1,::1

# Admin-användare
DEFAULT_ADMIN_PASSWORD=admin123!
```

### Lösenordspolicy

- **Minst 8 tecken**
- **Versaler och gemener**
- **Minst en siffra**
- **Minst ett specialtecken**
- **Inte vanliga lösenord**
- **Inte användarinformation**

## 🚨 Säkerhetsincidenter

### Automatiska varningar

Systemet varnar automatiskt för:

- **Brute force-attacker** (>20 misslyckade inloggningar/timme)
- **Misstänkt aktivitet** (>5 händelser/timme)
- **DoS-försök** (>50 rate limit-överträdelser/timme)
- **Systemfel** (>10% felfrekvens)
- **Obehörig åtkomst** (>10 försök/timme)

### Åtgärder vid incident

1. **Kontrollera säkerhetsloggar**
   ```bash
   GET /api/auth/security/logs?type=suspicious_activity
   ```

2. **Blockera misstänkta IP:er**
   - Lägg till i IP_WHITELIST (endast tillåtna)
   - Eller kontakta systemadministratör

3. **Återställ användaråtkomst**
   ```bash
   PUT /api/auth/users/{id}
   { "isActive": false }
   ```

4. **Rotera API-nycklar**
   ```bash
   PUT /api/auth/api-keys/{id}
   { "isActive": false }
   ```

## 🔧 Felsökning

### Vanliga fel

#### "Authentication required"
```bash
# Kontrollera token
curl -H "Authorization: Bearer YOUR_TOKEN" /api/auth/me

# Förnya token om utgången
curl -X POST /api/auth/refresh -d '{"refreshToken":"YOUR_REFRESH_TOKEN"}'
```

#### "Insufficient permissions"
```bash
# Kontrollera användarroll
GET /api/auth/me

# Kontakta admin för rolluppdatering
```

#### "Rate limit exceeded"
```bash
# Vänta tills rate limit återställs
# Kontrollera headers för reset-tid
# Kontakta admin om IP är blockerad
```

#### "CSRF token validation failed"
```bash
# Kontrollera att CSRF-token skickas
# Uppdatera session om nödvändigt
```

### Debug-kommandon

```bash
# Kontrollera säkerhetshälsa
GET /api/auth/security/health

# Se aktiva varningar
GET /api/auth/security/alerts

# Kontrollera rate limit-status
# (Finns i response headers)

# Se säkerhetsstatistik
GET /api/auth/security/statistics
```

## 📋 Säkerhetschecklista

### För administratörer

- [ ] Ändrat default admin-lösenord
- [ ] Skapat användare med lämpliga roller
- [ ] Konfigurerat IP-whitelist
- [ ] Satt upp säkerhetsövervakning
- [ ] Testat backup och återställning
- [ ] Dokumenterat säkerhetsrutiner

### För utvecklare

- [ ] Alla endpoints använder autentisering
- [ ] Behörigheter implementerade korrekt
- [ ] Input-validering på plats
- [ ] Säkerhetsloggar implementerade
- [ ] Säkerhetstester skrivna
- [ ] Dokumentation uppdaterad

### För operatörer

- [ ] Starkt lösenord valt
- [ ] Förstår rollbehörigheter
- [ ] Vet hur man rapporterar problem
- [ ] Loggar ut säkert
- [ ] Delar inte credentials

## 📚 Ytterligare resurser

- [Säkerhetsimplementation](../development/security-implementation.md) - Teknisk dokumentation
- [API-dokumentation](../api/) - Komplett API-referens
- [Utvecklingsguide](../development/) - För utvecklare
- [Användarguide](../user-guide/) - För slutanvändare

## 🆘 Support

Vid säkerhetsproblem:

1. **Akuta säkerhetshot**: Kontakta systemadministratör omedelbart
2. **Allmänna frågor**: Se dokumentation eller skapa issue
3. **Buggrapporter**: Använd issue tracker med säkerhetsmärkning
4. **Funktionsförfrågningar**: Diskutera med utvecklingsteamet

---

**⚠️ Viktigt**: Denna säkerhetsimplementation är designad för att skydda din RPA-applikation, men säkerhet är en kontinuerlig process. Håll systemet uppdaterat och övervaka regelbundet för nya hot.
