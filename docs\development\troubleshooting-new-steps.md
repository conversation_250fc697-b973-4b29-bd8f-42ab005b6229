# Felsökning: Nya RPA-steg

<PERSON> guide hj<PERSON><PERSON><PERSON> dig att lösa vanliga problem när du lägger till nya RPA-steg.

## Snabb Diagnos

### 1. "Unknown step type" fel

**Symptom:** `Error: Unknown step type: [stepType]` i konsolen

**Snabb fix:**
```bash
# 1. Kontrollera att steget finns i createStepFromType
grep -n "case '[stepType]'" shared/src/utils.ts

# 2. Kontrollera för duplicerade funktioner
grep -n "createStepFromType" shared/src/*.ts

# 3. Bygg om shared
npm run build:shared
```

**Vanliga orsaker:**
- Duplicerade `createStepFromType` funktioner
- Steget saknas i switch-satsen
- Shared-paketet inte byggt om efter ändringar

### 2. Steget visas inte i toolbar

**Kontrollera:**
- [ ] Steget finns i `frontend/src/components/flow-editor/step-definitions/[kate<PERSON>i].ts`
- [ ] Kategorin exporteras i `frontend/src/components/flow-editor/step-definitions/index.ts`
- [ ] Frontend byggt om: `npm run build:frontend`

### 3. Step-editor öppnas inte

**Kontrollera:**
- [ ] Steget finns i `STEP_CATEGORY_MAPPING` i `StepEditorRegistry.tsx`
- [ ] Editor-komponenten är korrekt implementerad
- [ ] Inga TypeScript-fel i editor-komponenten

### 4. Variabelnamn fungerar inte

**Kontrollera:**
- [ ] Steget finns i `getDefaultVariableName` i `shared/src/utils.ts`
- [ ] Steget finns i `VariableHelper.tsx` (båda ställena)
- [ ] Steget finns i `VariablesModal.tsx`

## Detaljerad Felsökning

### Steg 1: Verifiera Kod-struktur

```bash
# Kontrollera att alla nödvändiga filer finns
find . -name "*.ts" -o -name "*.tsx" | xargs grep -l "[stepType]"
```

### Steg 2: Kontrollera TypeScript-fel

```bash
# Kontrollera shared
cd shared && npx tsc --noEmit

# Kontrollera frontend
cd frontend && npx tsc --noEmit

# Kontrollera backend
cd backend && npx tsc --noEmit
```

### Steg 3: Kontrollera Build-process

```bash
# Bygg allt från början
npm run clean
npm install
npm run build:shared
npm run build:frontend
npm run build:backend
```

### Steg 4: Kontrollera Runtime

```bash
# Starta dev-server med debug
DEBUG=* npm run dev

# Eller kontrollera specifika loggar
npm run dev 2>&1 | grep -i error
```

## Vanliga Misstag

### 1. Glömmer uppdatera alla ställen

**Problem:** Steget fungerar delvis men inte helt.

**Lösning:** Använd checklistan i [adding-new-steps.md](./adding-new-steps.md#checklista-undvik-vanliga-fel)

### 2. Duplicerade funktioner

**Problem:** Gamla versioner av funktioner finns kvar.

**Lösning:**
```bash
# Hitta alla createStepFromType funktioner
grep -rn "createStepFromType" shared/src/

# Ta bort duplicerade funktioner
```

### 3. Cache-problem

**Problem:** Ändringar syns inte trots ombyggnad.

**Lösning:**
```bash
# Rensa all cache
rm -rf node_modules */node_modules
rm -rf */dist
rm -rf frontend/node_modules/.vite

# Installera och bygg om
npm install
npm run build
```

### 4. Import-problem

**Problem:** TypeScript kan inte hitta typer.

**Lösning:**
```typescript
// Kontrollera att importen är korrekt
import { YourStepType } from '@rpa-project/shared';

// Inte:
import { YourStepType } from '../../../shared/src/types';
```

## Debug-tips

### 1. Lägg till debug-loggar

```typescript
// I createStepFromType
export function createStepFromType(stepType: string): RpaStep {
  console.log('DEBUG: createStepFromType called with:', stepType);
  
  switch (stepType) {
    case 'yourStep':
      console.log('DEBUG: yourStep case matched');
      return { /* ... */ };
    default:
      console.log('DEBUG: default case reached with:', stepType);
      throw new Error(`Unknown step type: ${stepType}`);
  }
}
```

### 2. Kontrollera Network-fliken

- Öppna Developer Tools
- Gå till Network-fliken
- Kontrollera att rätt JavaScript-filer laddas
- Sök efter din step-typ i filerna

### 3. Använd React DevTools

- Installera React DevTools
- Kontrollera component-trädet
- Verifiera att rätt props skickas

## Få Hjälp

Om du fortfarande har problem:

1. **Kontrollera konsolen** för felmeddelanden
2. **Kör checklistan** i adding-new-steps.md
3. **Jämför med befintliga steg** som fungerar
4. **Skapa en minimal reproduktion** av problemet
5. **Dokumentera exakt vad du gjort** och vilka fel du får

## Se Också

- [Lägga till Nya RPA-steg](./adding-new-steps.md)
- [Arkitektur Översikt](./architecture.md)
- [Kodkonventioner](./conventions.md)
