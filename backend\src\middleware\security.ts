import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '@rpa-project/shared';
import { securityConfig } from '../config/security';
import { securityLogService } from '../services/auth/securityLogService';
import crypto from 'crypto';

/**
 * Input sanitization middleware
 */
export function sanitizeInput(req: Request, res: Response, next: NextFunction): any {
  // Sanitize request body
  if (req.body && typeof req.body === 'object') {
    req.body = sanitizeObject(req.body);
  }

  // Sanitize query parameters (create new object since query is read-only)
  if (req.query && typeof req.query === 'object') {
    const sanitizedQuery = sanitizeObject(req.query);
    Object.keys(req.query).forEach(key => delete (req.query as any)[key]);
    Object.assign(req.query, sanitizedQuery);
  }

  // Sanitize URL parameters
  if (req.params && typeof req.params === 'object') {
    const sanitizedParams = sanitizeObject(req.params);
    Object.keys(req.params).forEach(key => delete req.params[key]);
    Object.assign(req.params, sanitizedParams);
  }

  next();
}

/**
 * Recursively sanitize an object
 */
function sanitizeObject(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'string') {
    return sanitizeString(obj);
  }

  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item));
  }

  if (typeof obj === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      sanitized[sanitizeString(key)] = sanitizeObject(value);
    }
    return sanitized;
  }

  return obj;
}

/**
 * Sanitize a string to prevent XSS and injection attacks
 */
function sanitizeString(str: string): string {
  if (typeof str !== 'string') {
    return str;
  }

  return str
    // Remove null bytes
    .replace(/\0/g, '')
    // Remove control characters except newlines and tabs
    .replace(/[\x01-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
    // Limit length to prevent DoS
    .substring(0, 10000);
}

/**
 * CSRF protection middleware
 */
export function csrfProtection(req: Request, res: Response, next: NextFunction): any {
  // Skip CSRF for GET, HEAD, OPTIONS requests
  if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return next();
  }

  // Skip CSRF for API key authentication
  if (req.headers['x-api-key']) {
    return next();
  }

  const token = req.headers['x-csrf-token'] as string;
  const sessionToken = (req as any).session?.csrfToken;

  if (!token || !sessionToken || token !== sessionToken) {
    securityLogService.log({
      type: 'suspicious_activity',
      userId: req.auth?.user?.id,
      ip: getClientIP(req),
      userAgent: req.headers['user-agent'],
      endpoint: req.path,
      method: req.method,
      statusCode: 403,
      message: 'CSRF token validation failed',
      metadata: {
        providedToken: token ? 'present' : 'missing',
        sessionToken: sessionToken ? 'present' : 'missing',
      },
    });

    const response: ApiResponse = {
      success: false,
      error: 'CSRF token validation failed',
    };

    return res.status(403).json(response);
  }

  next();
}

/**
 * IP whitelist middleware
 */
export function ipWhitelist(req: Request, res: Response, next: NextFunction): any {
  const whitelist = securityConfig.security.ipWhitelist;
  
  if (!whitelist || whitelist.length === 0) {
    return next();
  }

  const clientIP = getClientIP(req);
  
  if (!whitelist.includes(clientIP)) {
    securityLogService.log({
      type: 'unauthorized_access',
      ip: clientIP,
      userAgent: req.headers['user-agent'],
      endpoint: req.path,
      method: req.method,
      statusCode: 403,
      message: `IP ${clientIP} not in whitelist`,
      metadata: {
        whitelist,
      },
    });

    const response: ApiResponse = {
      success: false,
      error: 'Access denied from this IP address',
    };

    return res.status(403).json(response);
  }

  next();
}

/**
 * Request size limiter middleware
 */
export function requestSizeLimit(maxSize: number = 10 * 1024 * 1024) { // 10MB default
  return (req: Request, res: Response, next: NextFunction): any => {
    const contentLength = parseInt(req.headers['content-length'] || '0');
    
    if (contentLength > maxSize) {
      securityLogService.log({
        type: 'suspicious_activity',
        userId: req.auth?.user?.id,
        ip: getClientIP(req),
        userAgent: req.headers['user-agent'],
        endpoint: req.path,
        method: req.method,
        statusCode: 413,
        message: `Request size ${contentLength} exceeds limit ${maxSize}`,
      });

      const response: ApiResponse = {
        success: false,
        error: 'Request entity too large',
      };

      return res.status(413).json(response);
    }

    next();
  };
}

/**
 * Suspicious activity detector middleware
 */
export function suspiciousActivityDetector(req: Request, res: Response, next: NextFunction): any {
  const userAgent = req.headers['user-agent'] || '';
  const ip = getClientIP(req);
  
  // Check for suspicious patterns
  const suspiciousPatterns = [
    /sqlmap/i,
    /nikto/i,
    /nmap/i,
    /burp/i,
    /owasp/i,
    /hack/i,
    /exploit/i,
    /injection/i,
    /script/i,
  ];

  const isSuspiciousUserAgent = suspiciousPatterns.some(pattern => pattern.test(userAgent));
  
  // Check for suspicious request patterns
  const suspiciousRequestPatterns = [
    /\.\./,  // Directory traversal
    /union.*select/i,  // SQL injection
    /<script/i,  // XSS
    /javascript:/i,  // XSS
    /eval\(/i,  // Code injection
    /exec\(/i,  // Code injection
  ];

  const requestString = `${req.path}${JSON.stringify(req.query)}${JSON.stringify(req.body)}`;
  const isSuspiciousRequest = suspiciousRequestPatterns.some(pattern => pattern.test(requestString));

  if (isSuspiciousUserAgent || isSuspiciousRequest) {
    securityLogService.log({
      type: 'suspicious_activity',
      userId: req.auth?.user?.id,
      ip,
      userAgent,
      endpoint: req.path,
      method: req.method,
      statusCode: 400,
      message: 'Suspicious activity detected',
      metadata: {
        suspiciousUserAgent: isSuspiciousUserAgent,
        suspiciousRequest: isSuspiciousRequest,
        patterns: suspiciousPatterns.filter(p => p.test(userAgent)).map(p => p.toString()),
      },
    });

    const response: ApiResponse = {
      success: false,
      error: 'Suspicious activity detected',
    };

    return res.status(400).json(response);
  }

  next();
}

/**
 * Security headers middleware
 */
export function securityHeaders(req: Request, res: Response, next: NextFunction): any {
  // Set security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  
  // Remove server information
  res.removeHeader('X-Powered-By');
  res.setHeader('Server', 'RPA-Server');

  // Set HSTS header for HTTPS
  if (req.secure || req.headers['x-forwarded-proto'] === 'https') {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  }

  next();
}

/**
 * Request timeout middleware
 */
export function requestTimeout(timeoutMs: number = 30000) { // 30 seconds default
  return (req: Request, res: Response, next: NextFunction): any => {
    const timeout = setTimeout(() => {
      if (!res.headersSent) {
        securityLogService.log({
          type: 'suspicious_activity',
          userId: req.auth?.user?.id,
          ip: getClientIP(req),
          userAgent: req.headers['user-agent'],
          endpoint: req.path,
          method: req.method,
          statusCode: 408,
          message: `Request timeout after ${timeoutMs}ms`,
        });

        const response: ApiResponse = {
          success: false,
          error: 'Request timeout',
        };

        res.status(408).json(response);
      }
    }, timeoutMs);

    // Clear timeout when response is sent
    res.on('finish', () => {
      clearTimeout(timeout);
    });

    next();
  };
}

/**
 * Generate CSRF token
 */
export function generateCSRFToken(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Get client IP address
 */
function getClientIP(req: Request): string {
  return (
    (req.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
    (req.headers['x-real-ip'] as string) ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    '127.0.0.1'
  );
}

/**
 * Validate request origin
 */
export function validateOrigin(req: Request, res: Response, next: NextFunction): any {
  const origin = req.headers.origin;
  const allowedOrigins = securityConfig.security.allowedOrigins;

  // Skip validation for same-origin requests
  if (!origin) {
    return next();
  }

  if (!allowedOrigins.includes(origin)) {
    securityLogService.log({
      type: 'unauthorized_access',
      ip: getClientIP(req),
      userAgent: req.headers['user-agent'],
      endpoint: req.path,
      method: req.method,
      statusCode: 403,
      message: `Invalid origin: ${origin}`,
      metadata: {
        origin,
        allowedOrigins,
      },
    });

    const response: ApiResponse = {
      success: false,
      error: 'Invalid origin',
    };

    return res.status(403).json(response);
  }

  next();
}

/**
 * Combined security middleware
 */
export function applySecurity() {
  return [
    securityHeaders,
    sanitizeInput,
    requestSizeLimit(),
    requestTimeout(),
    suspiciousActivityDetector,
    validateOrigin,
  ];
}

/**
 * Strict security middleware for sensitive endpoints
 */
export function applyStrictSecurity() {
  return [
    ...applySecurity(),
    ipWhitelist,
    csrfProtection,
  ];
}
