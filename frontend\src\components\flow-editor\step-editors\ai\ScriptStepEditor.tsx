import React, { useState, useCallback } from 'react';
import Editor from '@monaco-editor/react';
import { ScriptStep, getDefaultVariableName, validateStep } from '@rpa-project/shared';
import { BaseStepEditorProps, StepEditorLayout } from '../base/BaseStepEditor';

interface ScriptStepEditorProps extends BaseStepEditorProps {
  step: ScriptStep;
}

export const ScriptStepEditor: React.FC<ScriptStepEditorProps> = ({
  step,
  onSave,
  onCancel,
  compact = false,
  currentStepIndex = 0
}) => {
  const [testResult, setTestResult] = useState<string>('');
  const [isTestingScript, setIsTestingScript] = useState(false);

  const [editedStep, setEditedStep] = useState<ScriptStep>({ ...step });
  const [errors, setErrors] = useState<string[]>([]);

  const handleChange = (field: keyof ScriptStep, value: any) => {
    setEditedStep(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    const validation = validateStep(editedStep);
    if (!validation.isValid) {
      setErrors(validation.errors.map(e => e.message));
      return;
    }
    setErrors([]);
    onSave(editedStep);
  };

  const handleCodeChange = (value: string | undefined) => {
    handleChange('code', value || '');
  };

  // Test script execution with current variables
  const testScript = useCallback(async () => {
    if (!editedStep.code || !editedStep.code.trim()) {
      setTestResult('Fel: Ingen kod att testa');
      return;
    }

    setIsTestingScript(true);
    setTestResult('Testar script...');

    try {
      // Create a safe test environment
      const testVariables = {} as Record<string, any>;

      // Add some example variables if none exist
      if (Object.keys(testVariables).length === 0) {
        testVariables.exampleText = 'Hello World';
        testVariables.exampleNumber = 42;
        testVariables.exampleArray = [1, 2, 3];
      }

      // Create a function to test the code
      const testFunction = new Function('variables', `
        const console = {
          log: (...args) => {
            // Capture console.log output
            return args.map(arg => 
              typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
            ).join(' ');
          }
        };
        
        try {
          ${editedStep.code}
        } catch (error) {
          throw new Error('Script error: ' + error.message);
        }
      `);

      const result = testFunction(testVariables);
      
      setTestResult(`✅ Test lyckades!\nResultat: ${
        typeof result === 'object' 
          ? JSON.stringify(result, null, 2) 
          : String(result)
      }`);
    } catch (error) {
      setTestResult(`❌ Test misslyckades:\n${error instanceof Error ? error.message : 'Okänt fel'}`);
    } finally {
      setIsTestingScript(false);
    }
  }, [editedStep.code]);

  // Get default variable name
  const defaultVariableName = getDefaultVariableName('script', currentStepIndex);

  return (
    <StepEditorLayout
      title="Konfigurera Script-steg"
      errors={errors}
      onSave={handleSave}
      onCancel={onCancel}
      compact={compact}
    >
      {/* Main grid container - full width with 2 columns max */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '1rem',
        width: '100%'
      }}>

        {/* Namn - 1 column */}
        <div className="form-group">
          <label className="form-label">Namn</label>
          <input
            type="text"
            value={editedStep.name || ''}
            onChange={(e) => handleChange('name', e.target.value)}
            className="form-input"
            placeholder="Namn på script-steget"
          />
        </div>

        {/* Beskrivning - 1 column */}
        <div className="form-group">
          <label className="form-label">Beskrivning</label>
          <textarea
            value={editedStep.description || ''}
            onChange={(e) => handleChange('description', e.target.value)}
            className="form-input form-textarea"
            placeholder="Beskrivning av vad scriptet gör"
            rows={2}
          />
        </div>

        {/* Kodeditor - 2 columns (full width) */}
        <div style={{ gridColumn: '1 / -1' }} className="form-group">
          <label className="form-label">JavaScript-kod</label>
          <div style={{
            border: '1px solid #e5e7eb',
            borderRadius: '0.5rem',
            overflow: 'hidden',
            backgroundColor: 'white'
          }}>
            <Editor
              height="400px"
              defaultLanguage="javascript"
              value={editedStep.code || ''}
              onChange={handleCodeChange}
              theme="vs-light"
              options={{
                minimap: { enabled: false },
                scrollBeyondLastLine: false,
                fontSize: 14,
                lineNumbers: 'on',
                roundedSelection: false,
                automaticLayout: true,
                wordWrap: 'on',
                tabSize: 2,
                insertSpaces: true,
                padding: { top: 16, bottom: 16 }
              }}
            />
          </div>
          <div style={{
            marginTop: '0.75rem',
            padding: '0.75rem',
            backgroundColor: '#f8fafc',
            border: '1px solid #e5e7eb',
            borderRadius: '0.5rem',
            fontSize: '0.875rem',
            color: '#6b7280'
          }}>
            <div style={{ marginBottom: '0.5rem' }}>
              <strong style={{ color: '#1a0f0f' }}>Tillgängliga variabler:</strong> Inga variabler tillgängliga än
            </div>
            <div style={{ marginBottom: '0.5rem' }}>
              <strong style={{ color: '#1a0f0f' }}>Användning:</strong> Använd <code style={{
                backgroundColor: '#e5e7eb',
                padding: '0.125rem 0.25rem',
                borderRadius: '0.25rem',
                fontSize: '0.8rem'
              }}>variables.variabelnamn</code> för att komma åt variabler.
            </div>
            <div>
              <strong style={{ color: '#1a0f0f' }}>Returnera resultat:</strong> Använd <code style={{
                backgroundColor: '#e5e7eb',
                padding: '0.125rem 0.25rem',
                borderRadius: '0.25rem',
                fontSize: '0.8rem'
              }}>return värde;</code> för att returnera resultatet.
            </div>
          </div>
        </div>

        {/* Testa Script knapp - 1 column */}
        <div className="form-group">
          <button
            type="button"
            onClick={testScript}
            disabled={isTestingScript || !editedStep.code?.trim()}
            className="action-button secondary"
          >
            {isTestingScript ? 'Testar...' : 'Testa Script'}
          </button>
        </div>

        {/* Testresultat - 1 column */}
        {testResult && (
          <div className="form-group">
            <label className="form-label">Testresultat</label>
            <pre style={{
              background: testResult.startsWith('✅') ? '#f0f8f0' : '#f8f0f0',
              border: `1px solid ${testResult.startsWith('✅') ? '#4caf50' : '#f44336'}`,
              padding: '0.75rem',
              borderRadius: '0.5rem',
              fontSize: '0.75rem',
              whiteSpace: 'pre-wrap',
              maxHeight: '150px',
              overflow: 'auto',
              fontFamily: 'monospace'
            }}>
              {testResult}
            </pre>
          </div>
        )}

        {/* Variabelnamn - 1 column */}
        <div className="form-group">
          <label className="form-label">Variabelnamn för resultat</label>
          <input
            type="text"
            value={editedStep.variableName || ''}
            onChange={(e) => handleChange('variableName', e.target.value)}
            className="form-input"
            placeholder={defaultVariableName}
          />
          <div style={{
            fontSize: '0.75rem',
            color: '#6b7280',
            marginTop: '0.25rem'
          }}>
            Lämna tomt för att använda standardnamn: {defaultVariableName}
          </div>
        </div>

        {/* Timeout - 1 column */}
        <div className="form-group">
          <label className="form-label">Timeout (ms)</label>
          <input
            type="number"
            value={editedStep.timeout || 60000}
            onChange={(e) => handleChange('timeout', parseInt(e.target.value))}
            min="1000"
            max="300000"
            step="1000"
            className="form-input"
          />
          <div style={{
            fontSize: '0.75rem',
            color: '#6b7280',
            marginTop: '0.25rem'
          }}>
            Tid innan scriptet avbryts (1-300 sekunder)
          </div>
        </div>

      </div> {/* End of main grid container */}
    </StepEditorLayout>
  );
};
