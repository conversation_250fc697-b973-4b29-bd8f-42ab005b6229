import { SecurityLog, SecurityLogType } from './securityLogService';
import { securityLogService } from './securityLogService';
import { securityConfig } from '../../config/security';

export interface SecurityAlert {
  id: string;
  type: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  timestamp: Date;
  affectedIPs: string[];
  affectedUsers: string[];
  recommendations: string[];
  metadata?: Record<string, any>;
}

export interface SecurityMetrics {
  totalRequests: number;
  failedLogins: number;
  suspiciousActivity: number;
  rateLimitExceeded: number;
  unauthorizedAccess: number;
  uniqueIPs: number;
  uniqueUsers: number;
  averageResponseTime: number;
  topEndpoints: Array<{ endpoint: string; count: number }>;
  topUserAgents: Array<{ userAgent: string; count: number }>;
  errorRate: number;
}

/**
 * Security Monitoring Service
 */
export class SecurityMonitoringService {
  private alerts: SecurityAlert[] = [];
  private metrics: SecurityMetrics | null = null;
  private lastAnalysis: Date | null = null;
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor() {
    // Start monitoring
    this.startMonitoring();
  }

  /**
   * Start continuous security monitoring
   */
  startMonitoring(): void {
    // Run analysis every 5 minutes
    this.monitoringInterval = setInterval(() => {
      this.analyzeSecurityEvents();
    }, 5 * 60 * 1000);

    // Run initial analysis
    this.analyzeSecurityEvents();
  }

  /**
   * Stop monitoring
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  /**
   * Analyze security events and generate alerts
   */
  async analyzeSecurityEvents(): Promise<void> {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

      // Get recent logs
      const { logs } = await securityLogService.getLogs({
        startDate: oneHourAgo,
        endDate: now,
        limit: 10000,
      });

      // Calculate metrics
      this.metrics = this.calculateMetrics(logs);

      // Generate alerts
      const newAlerts = this.generateAlerts(logs, this.metrics);
      this.alerts.push(...newAlerts);

      // Keep only recent alerts (last 24 hours)
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      this.alerts = this.alerts.filter(alert => alert.timestamp >= oneDayAgo);

      this.lastAnalysis = now;

      // Log analysis results
      if (newAlerts.length > 0) {
        console.log(`🚨 Security analysis generated ${newAlerts.length} new alerts`);
        newAlerts.forEach(alert => {
          console.log(`   ${alert.type.toUpperCase()}: ${alert.title}`);
        });
      }

    } catch (error) {
      console.error('Error during security analysis:', error);
    }
  }

  /**
   * Calculate security metrics from logs
   */
  private calculateMetrics(logs: SecurityLog[]): SecurityMetrics {
    const totalRequests = logs.length;
    const failedLogins = logs.filter(log => log.type === 'login_failed').length;
    const suspiciousActivity = logs.filter(log => log.type === 'suspicious_activity').length;
    const rateLimitExceeded = logs.filter(log => log.type === 'rate_limit_exceeded').length;
    const unauthorizedAccess = logs.filter(log => log.type === 'unauthorized_access').length;

    const uniqueIPs = new Set(logs.map(log => log.ip)).size;
    const uniqueUsers = new Set(logs.filter(log => log.userId).map(log => log.userId)).size;

    // Calculate average response time from metadata
    const responseTimes = logs
      .filter(log => log.metadata?.duration)
      .map(log => log.metadata!.duration as number);
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
      : 0;

    // Top endpoints
    const endpointCounts = new Map<string, number>();
    logs.forEach(log => {
      const count = endpointCounts.get(log.endpoint) || 0;
      endpointCounts.set(log.endpoint, count + 1);
    });
    const topEndpoints = Array.from(endpointCounts.entries())
      .map(([endpoint, count]) => ({ endpoint, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Top user agents
    const userAgentCounts = new Map<string, number>();
    logs.forEach(log => {
      if (log.userAgent) {
        const count = userAgentCounts.get(log.userAgent) || 0;
        userAgentCounts.set(log.userAgent, count + 1);
      }
    });
    const topUserAgents = Array.from(userAgentCounts.entries())
      .map(([userAgent, count]) => ({ userAgent, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Error rate (4xx and 5xx status codes)
    const errorRequests = logs.filter(log => log.statusCode >= 400).length;
    const errorRate = totalRequests > 0 ? (errorRequests / totalRequests) * 100 : 0;

    return {
      totalRequests,
      failedLogins,
      suspiciousActivity,
      rateLimitExceeded,
      unauthorizedAccess,
      uniqueIPs,
      uniqueUsers,
      averageResponseTime,
      topEndpoints,
      topUserAgents,
      errorRate,
    };
  }

  /**
   * Generate security alerts based on logs and metrics
   */
  private generateAlerts(logs: SecurityLog[], metrics: SecurityMetrics): SecurityAlert[] {
    const alerts: SecurityAlert[] = [];

    // High number of failed logins
    if (metrics.failedLogins > 20) {
      alerts.push({
        id: `failed-logins-${Date.now()}`,
        type: 'high',
        title: 'High Number of Failed Login Attempts',
        description: `${metrics.failedLogins} failed login attempts detected in the last hour`,
        timestamp: new Date(),
        affectedIPs: this.getTopIPs(logs.filter(log => log.type === 'login_failed')),
        affectedUsers: [],
        recommendations: [
          'Review failed login attempts for patterns',
          'Consider implementing account lockout policies',
          'Check for brute force attacks',
          'Verify IP addresses are not from known malicious sources',
        ],
        metadata: { failedLogins: metrics.failedLogins },
      });
    }

    // Suspicious activity detected
    if (metrics.suspiciousActivity > 5) {
      alerts.push({
        id: `suspicious-activity-${Date.now()}`,
        type: 'high',
        title: 'Suspicious Activity Detected',
        description: `${metrics.suspiciousActivity} suspicious activities detected in the last hour`,
        timestamp: new Date(),
        affectedIPs: this.getTopIPs(logs.filter(log => log.type === 'suspicious_activity')),
        affectedUsers: [],
        recommendations: [
          'Investigate suspicious activity patterns',
          'Check for potential security scanning',
          'Review user agent strings for automated tools',
          'Consider blocking suspicious IP addresses',
        ],
        metadata: { suspiciousActivity: metrics.suspiciousActivity },
      });
    }

    // High rate limit violations
    if (metrics.rateLimitExceeded > 50) {
      alerts.push({
        id: `rate-limit-${Date.now()}`,
        type: 'medium',
        title: 'High Rate Limit Violations',
        description: `${metrics.rateLimitExceeded} rate limit violations in the last hour`,
        timestamp: new Date(),
        affectedIPs: this.getTopIPs(logs.filter(log => log.type === 'rate_limit_exceeded')),
        affectedUsers: [],
        recommendations: [
          'Review rate limit policies',
          'Check for potential DoS attacks',
          'Consider adjusting rate limits for legitimate users',
          'Implement progressive rate limiting',
        ],
        metadata: { rateLimitExceeded: metrics.rateLimitExceeded },
      });
    }

    // High error rate
    if (metrics.errorRate > 10) {
      alerts.push({
        id: `high-error-rate-${Date.now()}`,
        type: 'medium',
        title: 'High Error Rate',
        description: `Error rate is ${metrics.errorRate.toFixed(1)}% in the last hour`,
        timestamp: new Date(),
        affectedIPs: [],
        affectedUsers: [],
        recommendations: [
          'Investigate application errors',
          'Check server health and resources',
          'Review recent deployments',
          'Monitor database performance',
        ],
        metadata: { errorRate: metrics.errorRate },
      });
    }

    // Unauthorized access attempts
    if (metrics.unauthorizedAccess > 10) {
      alerts.push({
        id: `unauthorized-access-${Date.now()}`,
        type: 'medium',
        title: 'Multiple Unauthorized Access Attempts',
        description: `${metrics.unauthorizedAccess} unauthorized access attempts in the last hour`,
        timestamp: new Date(),
        affectedIPs: this.getTopIPs(logs.filter(log => log.type === 'unauthorized_access')),
        affectedUsers: [],
        recommendations: [
          'Review access control policies',
          'Check for privilege escalation attempts',
          'Verify authentication mechanisms',
          'Consider implementing additional access controls',
        ],
        metadata: { unauthorizedAccess: metrics.unauthorizedAccess },
      });
    }

    return alerts;
  }

  /**
   * Get top IP addresses from logs
   */
  private getTopIPs(logs: SecurityLog[], limit: number = 5): string[] {
    const ipCounts = new Map<string, number>();
    logs.forEach(log => {
      const count = ipCounts.get(log.ip) || 0;
      ipCounts.set(log.ip, count + 1);
    });

    return Array.from(ipCounts.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit)
      .map(([ip]) => ip);
  }

  /**
   * Get current security metrics
   */
  getMetrics(): SecurityMetrics | null {
    return this.metrics;
  }

  /**
   * Get current security alerts
   */
  getAlerts(type?: 'high' | 'medium' | 'low'): SecurityAlert[] {
    if (type) {
      return this.alerts.filter(alert => alert.type === type);
    }
    return [...this.alerts];
  }

  /**
   * Get security dashboard data
   */
  async getDashboardData(): Promise<{
    metrics: SecurityMetrics | null;
    alerts: SecurityAlert[];
    lastAnalysis: Date | null;
    recentActivity: SecurityLog[];
  }> {
    // Get recent activity (last 100 logs)
    const { logs: recentActivity } = await securityLogService.getLogs({
      limit: 100,
      offset: 0,
    });

    return {
      metrics: this.metrics,
      alerts: this.alerts,
      lastAnalysis: this.lastAnalysis,
      recentActivity,
    };
  }

  /**
   * Acknowledge an alert
   */
  acknowledgeAlert(alertId: string): boolean {
    const alertIndex = this.alerts.findIndex(alert => alert.id === alertId);
    if (alertIndex !== -1) {
      this.alerts.splice(alertIndex, 1);
      return true;
    }
    return false;
  }

  /**
   * Get security health score (0-100)
   */
  getSecurityHealthScore(): number {
    if (!this.metrics) {
      return 100; // No data means no problems detected
    }

    let score = 100;

    // Deduct points for security issues
    score -= Math.min(this.metrics.failedLogins * 2, 30); // Max 30 points for failed logins
    score -= Math.min(this.metrics.suspiciousActivity * 5, 25); // Max 25 points for suspicious activity
    score -= Math.min(this.metrics.unauthorizedAccess * 3, 20); // Max 20 points for unauthorized access
    score -= Math.min(this.metrics.errorRate, 15); // Max 15 points for error rate
    score -= Math.min(this.alerts.filter(a => a.type === 'high').length * 5, 10); // Max 10 points for high alerts

    return Math.max(score, 0);
  }
}

// Export singleton instance
export const securityMonitoringService = new SecurityMonitoringService();
