import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '@rpa-project/shared';
import { securityConfig } from '../config/security';
import { securityLogService } from '../services/auth/securityLogService';

interface RateLimitEntry {
  count: number;
  resetTime: number;
  firstRequest: number;
}

/**
 * Rate Limiter Service
 */
export class RateLimiter {
  private limits = new Map<string, RateLimitEntry>();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  /**
   * Check if request should be rate limited
   */
  checkLimit(
    key: string,
    windowMs: number,
    maxRequests: number,
    skipSuccessfulRequests = false,
    skipFailedRequests = false
  ): {
    allowed: boolean;
    remaining: number;
    resetTime: number;
    totalHits: number;
  } {
    const now = Date.now();
    const windowStart = now - windowMs;

    let entry = this.limits.get(key);

    // Create new entry if doesn't exist or window has expired
    if (!entry || entry.resetTime <= now) {
      entry = {
        count: 1,
        resetTime: now + windowMs,
        firstRequest: now,
      };
      this.limits.set(key, entry);

      return {
        allowed: true,
        remaining: maxRequests - 1,
        resetTime: entry.resetTime,
        totalHits: 1,
      };
    }

    // Increment counter
    entry.count++;

    const allowed = entry.count <= maxRequests;
    const remaining = Math.max(0, maxRequests - entry.count);

    return {
      allowed,
      remaining,
      resetTime: entry.resetTime,
      totalHits: entry.count,
    };
  }

  /**
   * Reset rate limit for a key
   */
  reset(key: string): void {
    this.limits.delete(key);
  }

  /**
   * Get current status for a key
   */
  getStatus(key: string): RateLimitEntry | null {
    return this.limits.get(key) || null;
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, entry] of this.limits.entries()) {
      if (entry.resetTime <= now) {
        this.limits.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 Cleaned up ${cleanedCount} expired rate limit entries`);
    }
  }

  /**
   * Get statistics
   */
  getStatistics() {
    const now = Date.now();
    let activeEntries = 0;
    let expiredEntries = 0;

    for (const entry of this.limits.values()) {
      if (entry.resetTime > now) {
        activeEntries++;
      } else {
        expiredEntries++;
      }
    }

    return {
      total: this.limits.size,
      active: activeEntries,
      expired: expiredEntries,
    };
  }

  /**
   * Destroy the rate limiter
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.limits.clear();
  }
}

// Global rate limiter instance
const rateLimiter = new RateLimiter();

/**
 * Get client identifier for rate limiting
 */
function getClientIdentifier(req: Request): string {
  // Use user ID if authenticated
  if (req.auth?.user?.id) {
    return `user:${req.auth.user.id}`;
  }

  // Use API key ID if using API key
  if (req.auth?.apiKey?.id) {
    return `api:${req.auth.apiKey.id}`;
  }

  // Fall back to IP address
  const ip = (
    (req.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
    (req.headers['x-real-ip'] as string) ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    '127.0.0.1'
  );

  return `ip:${ip}`;
}

/**
 * Create rate limiting middleware
 */
export function createRateLimit(
  windowMs: number,
  maxRequests: number,
  options: {
    skipSuccessfulRequests?: boolean;
    skipFailedRequests?: boolean;
    keyGenerator?: (req: Request) => string;
    message?: string;
    standardHeaders?: boolean;
    legacyHeaders?: boolean;
  } = {}
) {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const key = options.keyGenerator ? options.keyGenerator(req) : getClientIdentifier(req);
    
    const result = rateLimiter.checkLimit(
      key,
      windowMs,
      maxRequests,
      options.skipSuccessfulRequests,
      options.skipFailedRequests
    );

    // Add rate limit headers
    if (options.standardHeaders !== false) {
      res.set({
        'X-RateLimit-Limit': maxRequests.toString(),
        'X-RateLimit-Remaining': result.remaining.toString(),
        'X-RateLimit-Reset': new Date(result.resetTime).toISOString(),
      });
    }

    if (options.legacyHeaders) {
      res.set({
        'X-RateLimit-Window': windowMs.toString(),
        'X-RateLimit-Current': result.totalHits.toString(),
      });
    }

    if (!result.allowed) {
      // Log rate limit exceeded
      await securityLogService.log({
        type: 'rate_limit_exceeded',
        userId: req.auth?.user?.id,
        apiKeyId: req.auth?.apiKey?.id,
        ip: getClientIdentifier(req).startsWith('ip:') ? getClientIdentifier(req).substring(3) : '127.0.0.1',
        userAgent: req.headers['user-agent'],
        endpoint: req.path,
        method: req.method,
        statusCode: 429,
        message: `Rate limit exceeded: ${result.totalHits}/${maxRequests} requests in window`,
        metadata: {
          key,
          windowMs,
          maxRequests,
          totalHits: result.totalHits,
          resetTime: result.resetTime,
        },
      });

      const response: ApiResponse = {
        success: false,
        error: options.message || 'Too many requests, please try again later',
      };

      res.status(429).json(response);
      return;
    }

    next();
  };
}

/**
 * Global rate limiter middleware
 */
export const globalRateLimit = createRateLimit(
  securityConfig.rateLimits.global.windowMs,
  securityConfig.rateLimits.global.maxRequests,
  {
    message: 'Too many requests from this client, please try again later',
    standardHeaders: true,
  }
);

/**
 * Authentication rate limiter middleware
 */
export const authRateLimit = createRateLimit(
  securityConfig.rateLimits.auth.windowMs,
  securityConfig.rateLimits.auth.maxRequests,
  {
    skipSuccessfulRequests: securityConfig.rateLimits.auth.skipSuccessfulRequests,
    skipFailedRequests: securityConfig.rateLimits.auth.skipFailedRequests,
    message: 'Too many authentication attempts, please try again later',
    keyGenerator: (req: Request) => {
      // Use IP + username for auth rate limiting
      const ip = (
        (req.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
        (req.headers['x-real-ip'] as string) ||
        req.connection.remoteAddress ||
        '127.0.0.1'
      );
      const username = req.body?.username || 'unknown';
      return `auth:${ip}:${username}`;
    },
  }
);

/**
 * API rate limiter middleware
 */
export const apiRateLimit = createRateLimit(
  securityConfig.rateLimits.api.windowMs,
  securityConfig.rateLimits.api.maxRequests,
  {
    message: 'API rate limit exceeded, please try again later',
    keyGenerator: (req: Request) => {
      // Use API key ID if available, otherwise IP
      if (req.auth?.apiKey?.id) {
        return `api:${req.auth.apiKey.id}`;
      }
      const ip = (
        (req.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
        (req.headers['x-real-ip'] as string) ||
        req.connection.remoteAddress ||
        '127.0.0.1'
      );
      return `api:ip:${ip}`;
    },
  }
);

/**
 * Strict rate limiter for sensitive endpoints
 */
export const strictRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  5, // 5 requests
  {
    message: 'Too many requests to sensitive endpoint, please try again later',
  }
);

/**
 * Per-user rate limiter
 */
export function createUserRateLimit(windowMs: number, maxRequests: number) {
  return createRateLimit(windowMs, maxRequests, {
    keyGenerator: (req: Request) => {
      if (req.auth?.user?.id) {
        return `user:${req.auth.user.id}`;
      }
      // Fall back to IP if no user
      const ip = (
        (req.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
        (req.headers['x-real-ip'] as string) ||
        req.connection.remoteAddress ||
        '127.0.0.1'
      );
      return `ip:${ip}`;
    },
  });
}

/**
 * Reset rate limit for a user (admin function)
 */
export function resetUserRateLimit(userId: string): void {
  rateLimiter.reset(`user:${userId}`);
}

/**
 * Reset rate limit for an IP (admin function)
 */
export function resetIPRateLimit(ip: string): void {
  rateLimiter.reset(`ip:${ip}`);
}

/**
 * Get rate limit statistics
 */
export function getRateLimitStatistics() {
  return rateLimiter.getStatistics();
}

// Export the rate limiter instance for advanced usage
export { rateLimiter };
